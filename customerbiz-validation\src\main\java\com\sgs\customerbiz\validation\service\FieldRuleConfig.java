package com.sgs.customerbiz.validation.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSFieldValidationRulePO;
import lombok.Data;

import java.util.*;

@Data
public class FieldRuleConfig {
    private String ruleCode;
    private String paramTable;
    private RuleConfig ruleConfig;
    private List<String> modeList;

    public FieldRuleConfig ruleConfig(RuleConfig ruleConfig) {
        FieldRuleConfig newRuleConfig = new FieldRuleConfig();
        newRuleConfig.setRuleCode(ruleCode);
        newRuleConfig.setParamTable(paramTable);
        newRuleConfig.setRuleConfig(ruleConfig);
        return newRuleConfig;
    }

    public static FieldRuleConfig of(String ruleCode, String paramTable) {
        FieldRuleConfig fieldRuleConfig = new FieldRuleConfig();
        fieldRuleConfig.setRuleCode(ruleCode);
        fieldRuleConfig.setParamTable(paramTable);
        return fieldRuleConfig;
    }

    public static FieldRuleConfig createFrom(SGSFieldValidationRulePO fieldRulePO, RuleConfig ruleConfig) {
        FieldRuleConfig fieldRuleConfig = new FieldRuleConfig();
        fieldRuleConfig.setRuleCode(fieldRulePO.getRuleCode());
        fieldRuleConfig.setParamTable(fieldRulePO.getParamTable());
        fieldRuleConfig.setRuleConfig(ruleConfig);
        try {
            List<String> modes = JSON.parseObject(fieldRulePO.getModeList(), new TypeReference<List<String>>() {
            });
            fieldRuleConfig.setModeList(Optional.ofNullable(modes).orElse(Collections.emptyList()));
        } catch (Exception e) {
            fieldRuleConfig.setModeList(Collections.emptyList());
        }
        return fieldRuleConfig;
    }

    public Map<String, Object> paramTable() {
        if (paramTable == null) {
            return new HashMap<>();
        }
        return JSON.parseObject(paramTable, new TypeReference<Map<String, Object>>() {
        });
    }
}
