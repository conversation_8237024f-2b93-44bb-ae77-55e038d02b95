package com.sgs.customerbiz.validation.validator;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

public class TypeUtils {

    public static final Set<String> nowStr = ImmutableSet.of(
            "today", "now"
    );

    public static boolean isNumeric(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        if (obj instanceof Number) {
            return true;
        } else if (obj instanceof String) {
            if(StringUtils.isEmpty((String) obj)) {
                return true;
            }
            try {
                Double.parseDouble((String) obj);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        } else if (obj instanceof Collection) {
            for (Object element : (Collection<?>) obj) {
                if (!isNumeric(element)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public static Boolean convertToBoolean(Object obj) {
        if(Objects.isNull(obj)) {
            return false;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        if (obj instanceof String) {
            String str = (String) obj;
            return "true".equalsIgnoreCase(str) || "yes".equalsIgnoreCase(str) || "on".equalsIgnoreCase(str);
        }
        return false;
    }

    public static Object convertToNumber(Object obj) {
        if(Objects.isNull(obj)) {
            throw new IllegalArgumentException("can't convert null object");
        }
        if (obj instanceof Number) {
            return obj;  // 如果已经是数字类型，直接返回
        } else if (obj instanceof String) {
            try {
                return Double.parseDouble((String) obj);  // 尝试将字符串转换为Double
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("cannot convert " + obj + " to number", e);
            }
        }
        throw new IllegalArgumentException("cannot convert " + obj + " to number");
    }

    public static Integer convertToInteger(Object obj) {
        if(Objects.isNull(obj)) {
            throw new IllegalArgumentException("can't convert null object");
        }
        if (obj instanceof Integer) {
            return (Integer)obj;  // 如果已经是数字类型，直接返回
        } else if (obj instanceof String) {
            try {
                return Integer.parseInt((String) obj);  // 尝试将字符串转换为Double
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("cannot convert " + obj + " to number", e);
            }
        }
        throw new IllegalArgumentException("cannot convert " + obj + " to number");
    }

    public static Optional<Integer> tryConvertToInteger(Object obj) {
        try{
            return Optional.of(convertToInteger(obj));
        } catch (Throwable t) {
            return Optional.empty();
        }
    }

    public static boolean isStrings(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        if (obj instanceof String) {
            return true;
        } else if (obj instanceof Collection) {
            for (Object element : (Collection<?>) obj) {
                if (!isStrings(element)) {  // 递归检查集合中的每个元素
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public static boolean canConvertToLocalDate(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        if (obj instanceof LocalDate) {
            return true; // 已经是LocalDate
        } else if (obj instanceof LocalDateTime) {
            return true; // LocalDateTime可以转换为LocalDate
        } else if (obj instanceof Date) {
            return true; // Date可以通过转换为Instant再转换为LocalDate
        } else if (obj instanceof Long) {
            return isTimestamp((Long) obj); // 检查是否为有效的时间戳
        } else if (obj instanceof String) {
            if(StringUtils.isEmpty((String)obj)) {
                return true;
            }
            return isValidLocalDate((String) obj); // 检查字符串是否符合日期格式
        } else if (obj instanceof Collection) {
            for (Object element : (Collection<?>) obj) {
                if (!canConvertToLocalDate(element)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    private static boolean isTimestamp(Long timestamp) {
        try {
            LocalDate date = Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
            return true;
        } catch (DateTimeException e) {
            try {
                LocalDate date = Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
                return true;
            } catch (DateTimeException ex) {
                return false;
            }
        }
    }

    private static boolean isValidLocalDate(String dateString) {
        DateTimeFormatter[] formatters = new DateTimeFormatter[] {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("MM/dd/yyyy")
        };
        if(dateString.length()==7) {
            try {
                // 尝试解析字符串
                LocalDate.parse("01/"+dateString,  DateTimeFormatter.ofPattern("dd/MM/yyyy"));
                return true;
            } catch (DateTimeParseException e) {
                // 忽略解析错误，尝试下一个格式
            }
            return false;
        }
        for (DateTimeFormatter formatter : formatters) {
            try {
                // 尝试解析字符串
                LocalDate.parse(dateString, formatter);
                return true;
            } catch (DateTimeParseException e) {
                // 忽略解析错误，尝试下一个格式
            }
        }
        return false;
    }

    public static LocalDate convertToLocalDate(Object obj) {
        if (obj instanceof LocalDate) {
            return (LocalDate) obj;  // 如果是LocalDate类型，直接返回
        } else if (obj instanceof LocalDateTime) {
            return ((LocalDateTime) obj).toLocalDate();  // 如果是LocalDateTime类型，转换为LocalDate
        } else if (obj instanceof Date) {
            return ((Date) obj).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();  // Date转为LocalDate
        } else if (obj instanceof Long) {
            return parseTimestamp(((Long) obj));  // 处理时间戳转为LocalDate
        } else if (obj instanceof String) {
            return parseLocalDate((String) obj);  // 尝试解析字符串
        }
        throw new IllegalArgumentException("Unsupported type for conversion to LocalDate");
    }

    private static LocalDate parseTimestamp(Long timestamp) {
        try {
            return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
        } catch (DateTimeException e) {
            try {
                return Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
            } catch (DateTimeException ex) {
                throw e;
            }
        }
    }

    private static LocalDate parseLocalDate(String dateString) {
        if(nowStr.contains(dateString.toLowerCase())) {
            return LocalDate.now();
        }
        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        };
        for (DateTimeFormatter formatter : formatters) {
            try {
                // 可能只需要解析日期部分
                LocalDate date = LocalDate.parse(dateString, formatter);
                return date;
            } catch (DateTimeParseException e) {
                // 忽略异常，尝试下一个格式
            }
        }
        throw new DateTimeParseException("Failed to parse date: " + dateString, dateString, 0);
    }

    public static boolean canConvertToLocalDateTime(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        if (obj instanceof LocalDateTime) {
            return true; // 已经是LocalDateTime类型
        } else if (obj instanceof LocalDate) {
            return true; // LocalDate可以转换为LocalDateTime，假设时间为00:00
        } else if (obj instanceof Date) {
            return true; // Date可以转换为LocalDateTime
        } else if (obj instanceof Long) {
            return isTimestampValidForLocalDateTime((Long) obj); // 检查是否为有效的时间戳
        } else if (obj instanceof String) {
            if(StringUtils.isEmpty((String)obj)) {
                return true;
            }
            return isValidLocalDateTime((String) obj);
        } else if (obj instanceof Collection) {
            for (Object element : (Collection<?>) obj) {
                if (!canConvertToLocalDateTime(element)) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    private static boolean isTimestampValidForLocalDateTime(Long timestamp) {
        try {
            LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
            return true;
        } catch (DateTimeException e) {
            try {
                LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
                return true;
            } catch (DateTimeException ex) {
                return false;
            }
        }
    }

    private static boolean isValidLocalDateTime(String dateString) {
        DateTimeFormatter[] formatters = new DateTimeFormatter[] {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        };
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDateTime.parse(dateString, formatter);
                return true;
            } catch (DateTimeParseException e) {
                // 忽略解析错误，继续尝试下一个格式
            }
        }
        return false;
    }

    public static LocalDateTime convertToLocalDateTime(Object obj) {
        if (obj instanceof LocalDateTime) {
            return (LocalDateTime) obj;  // 如果已经是LocalDateTime类型，直接返回
        } else if (obj instanceof LocalDate) {
            return ((LocalDate) obj).atStartOfDay();  // 如果是LocalDate类型，转换为LocalDateTime，时间设为00:00
        } else if (obj instanceof Date) {
            return ((Date) obj).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();  // Date转为LocalDateTime
        } else if (obj instanceof Long) {
            return parseLocalDateTime(((Long) obj));  // 处理时间戳转为LocalDate
        } else if (obj instanceof String) {
            return parseLocalDateTime((String) obj);  // 尝试解析日期时间字符串
        }
        throw new IllegalArgumentException("Unsupported type for conversion to LocalDateTime");
    }

    private static LocalDateTime parseLocalDateTime(Long timestamp) {
        try {
            return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (DateTimeException e) {
            try {
                return Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } catch (DateTimeException ex) {
                throw e;
            }
        }
    }

    private static LocalDateTime parseLocalDateTime(String dateTimeString) {
        if(nowStr.contains(dateTimeString.toLowerCase())) {
            return LocalDateTime.now();
        }
        DateTimeFormatter[] formatters = new DateTimeFormatter[] {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        };
        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDateTime.parse(dateTimeString, formatter);
            } catch (DateTimeParseException e) {
                // 忽略解析错误，继续尝试下一个格式
            }
        }
        throw new DateTimeParseException("Failed to parse date time: " + dateTimeString, dateTimeString, 0);
    }

    public static boolean isObject(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        if(obj instanceof Collection){
            for (Object element : (Collection<?>) obj) {
                if (!isJSONObject(element)) {
                    return false;
                }
            }
            return true;
        }
        return isJSONObject(obj);
    }

    private static boolean isJSONObject(Object obj) {
        String jsonString = JSONObject.toJSONString(obj);
        return jsonString.startsWith("{");
    }

    public static boolean isCollection(Object obj) {
        if(Objects.isNull(obj)) {
            return true;
        }
        return obj instanceof Collection;
    }

    /**
     * 判断一个Object类型的参数是否为Java基本类型或其包装类型
     * 基本类型包括：byte, short, int, long, float, double, boolean, char
     * 包装类型包括：Byte, Short, Integer, Long, Float, Double, Boolean, Character
     *
     * @param obj 要检查的对象
     * @return 如果是基本类型或其包装类型返回true，否则返回false
     */
    public static boolean isPrimitiveType(Object obj) {
        if (Objects.isNull(obj)) {
            return false;
        }

        Class<?> clazz = obj.getClass();

        // 检查是否为基本类型的包装类
        return clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               // 虽然基本类型不会作为Object传递，但为了完整性也检查
               clazz == byte.class ||
               clazz == short.class ||
               clazz == int.class ||
               clazz == long.class ||
               clazz == float.class ||
               clazz == double.class ||
               clazz == boolean.class ||
               clazz == char.class;
    }

    /**
     * 判断一个Object类型的参数是否为Java基本数值类型或其包装类型
     * 数值类型包括：byte, short, int, long, float, double
     * 包装类型包括：Byte, Short, Integer, Long, Float, Double
     *
     * @param obj 要检查的对象
     * @return 如果是基本数值类型或其包装类型返回true，否则返回false
     */
    public static boolean isPrimitiveNumberType(Object obj) {
        if (Objects.isNull(obj)) {
            return false;
        }

        Class<?> clazz = obj.getClass();

        // 检查是否为基本数值类型的包装类
        return clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               // 虽然基本类型不会作为Object传递，但为了完整性也检查
               clazz == byte.class ||
               clazz == short.class ||
               clazz == int.class ||
               clazz == long.class ||
               clazz == float.class ||
               clazz == double.class;
    }

    public static void main(String[] args) {
        // 测试单个数字类型
        System.out.println(isNumeric(123)); // true
        System.out.println(isNumeric(3.14)); // true

        // 测试字符串
        System.out.println(isNumeric("456")); // true
        System.out.println(isNumeric("abc")); // false

        // 测试集合
        System.out.println(isNumeric(java.util.Arrays.asList(123, "456", 7.89))); // true
        System.out.println(isNumeric(java.util.Arrays.asList(123, "abc", 7.89))); // false
        System.out.println(isNumeric(java.util.Arrays.asList(java.util.Arrays.asList(123, "456"), 7.89))); // true

        // 测试单个字符串
        System.out.println(isStrings("Hello, world!")); // true

        // 测试非字符串对象
        System.out.println(isStrings(123)); // false

        // 测试字符串集合
        System.out.println(isStrings(java.util.Arrays.asList("Hello", "World"))); // true

        // 测试混合类型的集合
        System.out.println(isStrings(java.util.Arrays.asList("Hello", 123))); // false

        // 测试嵌套集合，全部是字符串
        System.out.println(isStrings(java.util.Arrays.asList(java.util.Arrays.asList("Nested", "Strings"), "Still good"))); // true

        // 测试嵌套集合，含有非字符串
        System.out.println(isStrings(java.util.Arrays.asList(java.util.Arrays.asList("Nested", "Strings"), 789))); // false

        // 测试不同类型的输入
        System.out.println(canConvertToLocalDate("2020-01-01")); // true
        System.out.println(canConvertToLocalDate("2020-01-01 12:00:00")); // true
        System.out.println(canConvertToLocalDate(LocalDate.now())); // true
        System.out.println(canConvertToLocalDate(LocalDateTime.now())); // true
        System.out.println(canConvertToLocalDate(new Date())); // true
        System.out.println(canConvertToLocalDate("Not a date")); // false
        System.out.println(canConvertToLocalDate(java.util.Arrays.asList("2020-01-01", new Date()))); // true
        System.out.println(canConvertToLocalDate(java.util.Arrays.asList("Invalid date", "2020-01-01"))); // false

        // 测试不同类型的输入
        System.out.println(canConvertToLocalDateTime("2020-01-01")); // true
        System.out.println(canConvertToLocalDateTime("2020-01-01 12:00:00")); // true
        System.out.println(canConvertToLocalDateTime(LocalDate.now())); // true
        System.out.println(canConvertToLocalDateTime(LocalDateTime.now())); // true
        System.out.println(canConvertToLocalDateTime(new Date())); // true
        System.out.println(canConvertToLocalDateTime("Not a date")); // false
        System.out.println(canConvertToLocalDateTime(java.util.Arrays.asList("2020-01-01", new Date()))); // true
        System.out.println(canConvertToLocalDateTime(java.util.Arrays.asList("Invalid date", "2020-01-01"))); // false

        // 测试基本类型判断
        System.out.println("=== 测试基本类型判断 ===");
        System.out.println("isPrimitiveType(123): " + isPrimitiveType(123)); // true (Integer)
        System.out.println("isPrimitiveType(3.14): " + isPrimitiveType(3.14)); // true (Double)
        System.out.println("isPrimitiveType(true): " + isPrimitiveType(true)); // true (Boolean)
        System.out.println("isPrimitiveType('A'): " + isPrimitiveType('A')); // true (Character)
        System.out.println("isPrimitiveType(\"Hello\"): " + isPrimitiveType("Hello")); // false (String)
        System.out.println("isPrimitiveType(new Date()): " + isPrimitiveType(new Date())); // false (Date)
        System.out.println("isPrimitiveType(null): " + isPrimitiveType(null)); // false

        System.out.println("=== 测试基本数值类型判断 ===");
        System.out.println("isPrimitiveNumberType(123): " + isPrimitiveNumberType(123)); // true (Integer)
        System.out.println("isPrimitiveNumberType(3.14f): " + isPrimitiveNumberType(3.14f)); // true (Float)
        System.out.println("isPrimitiveNumberType(100L): " + isPrimitiveNumberType(100L)); // true (Long)
        System.out.println("isPrimitiveNumberType(true): " + isPrimitiveNumberType(true)); // false (Boolean)
        System.out.println("isPrimitiveNumberType('A'): " + isPrimitiveNumberType('A')); // false (Character)
        System.out.println("isPrimitiveNumberType(\"123\"): " + isPrimitiveNumberType("123")); // false (String)
    }

}
