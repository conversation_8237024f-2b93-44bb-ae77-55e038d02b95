package com.sgs.customerbiz.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.google.common.base.Joiner;
import com.sgs.customerbiz.biz.convert.impl.fn.GetMappingValueFn;
import com.sgs.customerbiz.biz.utils.CollectDataUtil;
import com.sgs.customerbiz.biz.utils.ScheduledWhiteList;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductDTO;
import com.sgs.customerbiz.model.trf.dto.TrfProductSampleDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.validation.filter.FilterContext;
import com.sgs.customerbiz.validation.newtypes.CustomerGroupNo;
import com.sgs.customerbiz.validation.newtypes.CustomerNo;
import com.sgs.customerbiz.validation.newtypes.RefSystemId;
import com.sgs.customerbiz.validation.props.ValidationProps;
import com.sgs.customerbiz.validation.request.ModelValidationReq;
import com.sgs.customerbiz.validation.service.*;
import com.sgs.customerbiz.validation.utils.DffUtils;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class ValidationBizService {

    private final ValidationConfigService validationConfigService;

    private final ValidationFilterService validationFilterService;

    private final ValidationDffService validationDffService;

    private final ValidationService validationService;

    private final ValidationMessageService validationMessageService;

    private final ValidationProps props;

    private final EmailService emailService;

    private final DffClient dffClient;

    private final ScheduledWhiteList scheduledWhiteList;

    private final StarLimsService starLimsService;

    public ValidationBizService(ValidationConfigService validationConfigService, ValidationFilterService validationFilterService, ValidationDffService validationDffService,
                                ValidationService validationService,
                                ValidationMessageService validationMessageService, ValidationProps props,
                                EmailService emailService, DffClient dffClient,
                                ScheduledWhiteList scheduledWhiteList,
                                StarLimsService starLimsService) {
        this.validationConfigService = validationConfigService;
        this.validationFilterService = validationFilterService;
        this.validationDffService = validationDffService;
        this.validationService = validationService;
        this.validationMessageService = validationMessageService;
        this.props = props;
        this.emailService = emailService;
        this.dffClient = dffClient;
        this.scheduledWhiteList = scheduledWhiteList;
        this.starLimsService = starLimsService;
    }

    public void validate(TrfSyncReq request) {
        TrfSyncReq req = request;
        //SCI-1363
        if (SgsSystem.check(req.getSystemId(), SgsSystem.STARLIMS)) {
            req = JSON.parseObject(JSON.toJSONString(request), TrfSyncReq.class);
            starLimsService.handleTemplateId(req);
        }
        Integer refSystemId = req.getHeader().getRefSystemId();
        String buCode = req.getBuCode();
        String labCode = req.getLabCode();
        Stream<ValidationData> validationDataStream = CollectDataUtil.splitByReport(req).stream()
                .map(ValidationData::getValidationData);
        validateThenThrow(refSystemId, buCode, labCode, validationDataStream, req);
    }

    public List<ValidationResult> validate(ModelValidationReq modelValidationReq) {
        // 检查是否存在引用的系统ID，如果存在且在白名单中，对数据进行标准化处理
        if (Objects.nonNull(modelValidationReq.getRefSystemId())) {
            Integer refSystemId = modelValidationReq.getRefSystemId();
            if (scheduledWhiteList.allowNormDffData(refSystemId)) {
                Object normed = DffUtils.normLabelCodeAndLabelValue(modelValidationReq.getData());
                modelValidationReq.setData(normed);
            }
        }

        // 根据请求查找相应的验证配置，进行数据验证，并返回验证结果
        return findConfig(modelValidationReq)
                .flatMap(validationConfigs -> filterBy(modelValidationReq, validationConfigs))
                .map(validationConfigs -> {
                    // 对数据进行分割，并对每部分数据进行验证，收集所有的验证结果
                    return CollectDataUtil.splitByReport(modelValidationReq.getData()).stream()
                            .flatMap(data -> validationService.validate(validationConfigs, ValidationData.getValidationData(data)).stream())
                            .collect(Collectors.toList());
                })
                .orElse(Collections.emptyList());
    }

    public boolean existsValidation(ModelValidationReq modelValidationReq) {
        // 检查是否存在引用的系统ID，如果存在且在白名单中，对数据进行标准化处理
        if (Objects.nonNull(modelValidationReq.getRefSystemId())) {
            Integer refSystemId = modelValidationReq.getRefSystemId();
            if (scheduledWhiteList.allowNormDffData(refSystemId)) {
                Object normed = DffUtils.normLabelCodeAndLabelValue(modelValidationReq.getData());
                modelValidationReq.setData(normed);
            }
        }

        // 根据请求查找相应的验证配置，进行数据验证，并返回验证结果
        return findConfig(modelValidationReq)
                .flatMap(validationConfigs -> filterBy(modelValidationReq, validationConfigs))
                .isPresent();
    }


    private void validateThenThrow(Integer refSystemId, String buCode, String labCode, Stream<ValidationData> validationDataList, TrfSyncReq req) {
        validationDataList
                .map(validationData -> validate(refSystemId, validationData, req))
                .map(results ->
                        results.ifCustomizedErrors(errors -> {
                            List<String> errorMessages = validationMessageService.formatMessage(errors, false);
                            emailService.sendWhenSyncTrfValidation(refSystemId, buCode, labCode, errorMessages);
                        })
                )
                .reduce(ValidationResults.identity, ValidationResults::merge)
                .ifErrors(errors -> {
                    List<String> errorMessages = validationMessageService.formatMessage(errors, false);
                    ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.VALIDATORBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUIREDMISSING);
                    throw new CustomerBizException(errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), Joiner.on(";").join(errorMessages));
                    // throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, Joiner.on(";").join(errorMessages));
                });
    }

    private ValidationResults validate(Integer refSystemId, ValidationData validationData, TrfSyncReq syncReq) {
        List<ValidationResult> validationResults = findConfig(syncReq.getProductLineCode(), refSystemId)
                .flatMap(validationConfigs -> filterBy(syncReq, validationConfigs))
                .map(validationConfigs -> validationService.validate(validationConfigs, validationData))
                .orElse(Collections.emptyList());
        return ValidationResults.of(validationResults);
    }

    private Optional<List<FieldValidationConfig>> findConfig(ModelValidationReq modelValidationReq) {

        if (Objects.nonNull(modelValidationReq.getRefSystemId())) {
            Optional<List<FieldValidationConfig>> customer = validationConfigService.findBy(modelValidationReq.getProductLineCode(), new RefSystemId(modelValidationReq.getRefSystemId()));
            if (props.getListOfSystemIdToMergeBasic().contains(modelValidationReq.getSystemId())) {
                Optional<List<FieldValidationConfig>> basic = validationConfigService.findBasic();
                Optional<List<FieldValidationConfig>> result = validationConfigService.prepareJoin().basic(basic).customer(customer).join();
                return result;
            }
            return customer;
        }
        if (StringUtils.isNotEmpty(modelValidationReq.getCustomerGroupCode())) {
            return validationConfigService.findBy(modelValidationReq.getProductLineCode(), new CustomerGroupNo(modelValidationReq.getCustomerGroupCode()));
        }
        if (StringUtils.isNotEmpty(modelValidationReq.getCustomerNo())) {
            return validationConfigService.findBy(modelValidationReq.getProductLineCode(), new CustomerNo(modelValidationReq.getCustomerNo()));
        }
        return Optional.empty();
    }

    private Optional<List<FieldValidationConfig>> findConfig(String productLineCode, Integer refSystemId) {
        return this.validationConfigService.findBy(productLineCode, new RefSystemId(refSystemId));
    }


    private @NotNull Optional<List<FieldValidationConfig>> filterBy(ModelValidationReq modelValidationReq, List<FieldValidationConfig> validationConfigs) {
        // 解析订单数据中第一个产品的模板ID作为formTemplate
        String formGroupId = Optional.ofNullable(JSONPath.eval(modelValidationReq.getData(), "$.orderList[0].productList[0].templateId"))
                .map(Object::toString)
                .map(dffClient::queryDffFormGeneral)
                .flatMap(DffClient::firstFormGroupId)
                .orElse("");
        // 解析订单数据中第一个样本的模板ID作为gridTemplate
        String gridGroupId = Optional.ofNullable(JSONPath.eval(modelValidationReq.getData(), "$.orderList[0].sampleList[0].templateId"))
                .map(Object::toString)
                .map(dffClient::queryDffFormGeneral)
                .flatMap(DffClient::firstFormGroupId)
                .orElse("");
        ModelValidationReq.ModelValidationReqFilterContext filterCtx = modelValidationReq.toFilterCtx(formGroupId, gridGroupId);
        Optional<List<FieldValidationConfig>> filter = validationFilterService.filter(validationConfigs, filterCtx);
        filter.ifPresent(configs -> {
            Map<String, String> dffCodeMapping = validationDffService.findDffCodeMapping(modelValidationReq, formGroupId, gridGroupId);
             ValidationConfigService.replaceDffCode(dffCodeMapping, configs, props);
        });
        return filter;
    }

    private @NotNull Optional<List<FieldValidationConfig>> filterBy(TrfSyncReq syncReq, List<FieldValidationConfig> validationConfigs) {
        // 解析订单数据中第一个产品的模板ID作为formTemplate
        String formGroupId = Optional.of(syncReq.getOrder())
                .map(TrfOrderDTO::getProductList)
                .filter(CollectionUtils::isNotEmpty)
                .map(products -> products.get(0))
                .map(TrfProductDTO::getTemplateId)
                .map(dffClient::queryDffFormGeneral)
                .flatMap(DffClient::firstFormGroupId)
                .orElse("");
        // 解析订单数据中第一个样本的模板ID作为gridTemplate
        String gridGroupId = Optional.of(syncReq.getOrder())
                .map(TrfOrderDTO::getSampleList)
                .filter(CollectionUtils::isNotEmpty)
                .map(products -> products.get(0))
                .map(TrfProductSampleDTO::getTemplateId)
                .map(dffClient::queryDffFormGeneral)
                .flatMap(DffClient::firstFormGroupId)
                .orElse("");
        Optional<List<FieldValidationConfig>> filter = validationFilterService.filter(validationConfigs, new TrfSyncReqFilterContext(syncReq, formGroupId, gridGroupId));
        filter.ifPresent(configs -> {
            Map<String, String> dffCodeMapping = validationDffService.findDffCodeMapping(syncReq, formGroupId, gridGroupId);
            ValidationConfigService.replaceDffCode(dffCodeMapping, configs, props);
        });
        return filter;
    }

    public static class TrfSyncReqFilterContext implements FilterContext {

        private final TrfSyncReq trfSyncReq;
        private final String gridGroupId;
        private final String formGroupId;

        public TrfSyncReqFilterContext(TrfSyncReq trfSyncReq, String formGroupId, String gridGroupId) {
            this.trfSyncReq = trfSyncReq;
            this.formGroupId = formGroupId;
            this.gridGroupId = gridGroupId;
        }

        @Override
        public String getMode() {
            return trfSyncReq.getAction();
        }

        @Override
        public Integer getRefSystemId() {
            return trfSyncReq.getHeader().getRefSystemId();
        }

        @Override
        public Integer getSystemId() {
            return trfSyncReq.getSystemId();
        }

        @Override
        public String getProductLineCode() {
            return StringUtils.isNotBlank(trfSyncReq.getBuCode()) ? trfSyncReq.getBuCode() : trfSyncReq.getProductLineCode();
        }

        @Override
        public Object getStandardModel() {
            return trfSyncReq;
        }

        @Override
        public String getGirdGroupId() {
            return gridGroupId;
        }

        @Override
        public String getFormGroupId() {
            return formGroupId;
        }
    }

}
