package com.sgs.customerbiz.facade.model.trf.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotNull;
import java.util.List;
@ApiModel
public final class CustomerTrfInfoReq extends BaseRequest {
    /**
     *
     */
    @NotNull(message = "refSystemId 不能为空")
    private Integer refSystemId;

    /**
     *
     */
    @NotNull(message = "trfNos 不能为空")
    private List<String> trfNos;

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    public List<String> getTrfNos() {
        return trfNos;
    }

    public void setTrfNos(List<String> trfNos) {
        this.trfNos = trfNos;
    }

    @Override
    public String toString() {
        return "{" +
                "refSystemId=" + refSystemId +
                ", trfNos=" + trfNos +
                '}';
    }
}
