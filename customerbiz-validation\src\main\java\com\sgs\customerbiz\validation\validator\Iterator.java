package com.sgs.customerbiz.validation.validator;

import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import com.sgs.customerbiz.validation.service.parser.ValueParser;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class Iterator implements MarkedRule{


    public List<ValidationResult> iterator(Object value, FieldValidationConfig fieldValidationConfig, Map<String, Rule> ruleMap, Map<String, ValueParser> valueParserMap) {
        return null;
    }

}
