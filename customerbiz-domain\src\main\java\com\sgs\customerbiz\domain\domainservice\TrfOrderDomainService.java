package com.sgs.customerbiz.domain.domainservice;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.core.util.HeaderHelper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfOrderExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfChildOrderMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfOrderLogMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfOrderMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.domainobject.v2.TrfChildOrderDO;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfOrderStatusEnum;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.enums.ActiveIndicatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TrfOrderDomainService {

    @Resource
    private IdService idService;

    @Resource
    private TrfOrderMapper trfOrderMapper;

    private final TrfOrderExtMapper trfOrderExtMapper;

    @Autowired
    private TrfChildOrderMapper trfChildOrderMapper;

    @Resource
    private TrfOrderLogMapper trfOrderLogMapper;

    @Resource
    private TrfInfoMapper trfInfoMapper;

    @Autowired
    private TrfChildOrderMapper childOrderMapper;

    public TrfOrderDomainService(TrfOrderExtMapper trfOrderExtMapper) {
        this.trfOrderExtMapper = trfOrderExtMapper;
    }

    public List<TrfOrderDOV2> selectByTrfId(Long trfId) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        trfOrderExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(trfOrderExample);
        return convertTrfOrderDO(trfOrderPOS);
    }

    public List<TrfOrderPO> selectOrderListByTrfId(Long trfId) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        trfOrderExample.createCriteria()
                .andTrfIdEqualTo(trfId)
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
        return trfOrderMapper.selectByExample(trfOrderExample);
    }

    public void bind(TrfSyncHeaderDTO syncHeader, Long trfId, TrfOrderDOV2 trfOrder) {
        // 判断多个待绑定的trf之中是存在1vN模式的trf
        checkTrf1vN(syncHeader, trfOrder);
        bind(trfId, trfOrder);
    }

    public void checkTrf1vN(TrfSyncHeaderDTO syncHeader, TrfOrderDOV2 trfOrder) {
        List<String> trfNoList = syncHeader.getTrfList().stream().map(TrfHeaderDTO::getTrfNo).distinct().collect(Collectors.toList());
        TrfInfoExample example = new TrfInfoExample();
        example.createCriteria().
                andTrfNoIn(trfNoList).
                andRefSystemIdEqualTo(syncHeader.getRefSystemId()).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(trfInfoPOList)) {
            log.error("TRFs were not found. TrfNoList={}", JSON.toJSONString(trfNoList));
            throw new BizException("TRFs were not found.");
        }

        boolean anyMatchedAs1vN = trfInfoPOList.stream().anyMatch(trfInfoPO -> {
                    // 历史为Null的默认是N:1模式，之后版本不允许为Null
                    if (trfInfoPO.getIntegrationLevel() == null) {
                        return false;
                    }
                    return Objects.equals(Integer.parseInt(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule());
                }
        );

        // 存在1vN模式的trf
        if (anyMatchedAs1vN) {
            if (trfInfoPOList.size() > 1) {
                log.error("The trf in 1vN mode cannot be bound to the same order as other trfs.OrderNo={}, TrfIdList={}",
                        trfOrder.getOrderNo(), JSON.toJSONString(trfNoList));
                throw new BizException("The trf in 1vN mode cannot be bound to the same order as other trfs.");
            }
        }
    }

    public Integer selectOrderStatus(String orderNo, Integer systemId) {
        TrfOrderExample orderExample = new TrfOrderExample();
        orderExample.createCriteria().
                andOrderNoEqualTo(orderNo).
                andSystemIdEqualTo(systemId).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(orderExample);
        if (Func.isNotEmpty(trfOrderPOS)) {
            return trfOrderPOS.get(0).getOrderStatus();
        }
        return null;
    }

    public void bind(Long trfId, TrfOrderDOV2 trfOrder) {
        checkParam(trfId, trfOrder);

        TrfInfoPO trfInfoPO = trfInfoMapper.selectByPrimaryKey(trfId);
        Assert.notNull(trfInfoPO, "trf by Id " + trfId + " not found!");
        if (Func.isBlank(trfInfoPO.getIntegrationLevel())) {
            trfInfoPO.setIntegrationLevel(TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule().toString());
        }
        checkIntegrationLevel(trfOrder, trfInfoPO);

        TrfOrderExample orderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = orderExample.createCriteria().
                andOrderNoEqualTo(trfOrder.getOrderNo()).
                andSystemIdEqualTo(trfOrder.getSystemId()).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).
                andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
        if (Func.isNotEmpty(trfOrder.getOrderId())) {
            criteria.andOrderIdEqualTo(trfOrder.getOrderId());
        }
        List<TrfOrderPO> trfOrderRefList = trfOrderMapper.selectByExample(orderExample);
        if(Func.isEmpty(trfOrderRefList) && Func.isNotEmpty(trfOrder.getRealOrderNo())) {
            TrfOrderExample orderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNO = orderExampleByRealOrderNo.createCriteria().
                    andOrderNoEqualTo(trfOrder.getRealOrderNo()).
                    andSystemIdEqualTo(trfOrder.getSystemId()).
                    andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).
                    andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(trfOrder.getOrderId())) {
                criteriaByRealOrderNO.andOrderIdEqualTo(trfOrder.getOrderId());
            }
            trfOrderRefList = trfOrderMapper.selectByExample(orderExampleByRealOrderNo);
        }
        if (CollUtil.isNotEmpty(trfOrderRefList)) {

            boolean hasBound = trfOrderRefList.stream().anyMatch(trfOrderRef -> Objects.equals(trfId, trfOrderRef.getTrfId()));
            if (hasBound) {
                log.info("TRF[trfId={}] had bound with order[orderNo={}].", trfId, trfOrder.getOrderNo());
                return;
            }
            // 1vN模式的trf将绑定的订单是否绑定过其他trf
            // trfInfoPO.getIntegrationLevel() == null -> Nv1模式
            if (trfInfoPO.getIntegrationLevel() != null &&
                    (Objects.equals(Integer.parseInt(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule()) ||
                            Objects.equals(Integer.parseInt(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_ONE.getRule()) )
            ) {
                log.error("Trf in 1vN mode cannot be bound to the order already bound to other trf. TrfId={}, OrderNo={}", trfId, trfOrder.getOrderNo());
                throw new BizException("Trf in 1vN mode cannot be bound to the order already bound to other trf");
            }
        }

        if(Objects.equals(trfInfoPO.getSource(), TrfSourceType.Order2TRF.getSourceType())) {
            List<TrfOrderDOV2> bindOrderList = selectByTrfId(trfInfoPO.getId());
            if(CollectionUtils.isNotEmpty(bindOrderList)) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                        ErrorCategoryEnum.BUSINESS_ERROR,
                        ErrorBizModelEnum.SCITRFBIZSERVICE,
                        ErrorFunctionTypeEnum.BIND,
                        ErrorTypeEnum.REVISE_STATUS_ERROR);
                throw new CustomerBizException(errorCode, "Trf source is OrderToTrf. Other Orders cannot be bound to this Trf. ");
            }
        }

        Date now = DateUtil.now();
        TrfOrderPO trfOrderPO = new TrfOrderPO();
        trfOrderPO.setOrderExpectDueDate(trfOrder.getOrderExpectDueDate());
        trfOrderPO.setId(idService.nextId());
        trfOrderPO.setTrfId(trfId);
        trfOrderPO.setTrfNo(trfInfoPO.getTrfNo());
        trfOrderPO.setRefSystemId(trfInfoPO.getRefSystemId());
        trfOrderPO.setSystemId(trfOrder.getSystemId());
        trfOrderPO.setOrderId(trfOrder.getOrderId());
        trfOrderPO.setEnquiryNo(trfOrder.getEnquiryNo());
        trfOrderPO.setOrderNo(trfOrder.getOrderNo());
        trfOrderPO.setOrderStatus(Func.isNotEmpty(trfOrder.getOrderStatus()) ? trfOrder.getOrderStatus() : 3);
        trfOrderPO.setBoundStatus(ActiveIndicatorEnum.Active.getStatus());
        trfOrderPO.setActiveIndicator(ActiveIndicatorEnum.Active.getStatus());
        trfOrderPO.setCreatedBy(Func.isNotEmpty(trfOrder.getOperator()) ? trfOrder.getOperator() : USER_DEFAULT);
        trfOrderPO.setCreatedDate(Func.isNotEmpty(trfOrder.getOperationTime()) ? trfOrder.getOperationTime() : now);
        trfOrderPO.setModifiedBy(Func.isNotEmpty(trfOrder.getOperator()) ? trfOrder.getOperator() : USER_DEFAULT);
        trfOrderPO.setModifiedDate(Func.isNotEmpty(trfOrder.getOperationTime()) ? trfOrder.getOperationTime() : now);
        trfOrderMapper.insert(trfOrderPO);

        // 保存childOrder
        saveChildOrderList(trfOrder.getSystemId(), trfOrder.getOrderNo(), trfOrder.getOrderId(), trfOrder.getChildOrderList());

        logTrfOrderChanged(Collections.singletonList(trfOrderPO));
    }

    public static void checkIntegrationLevel(TrfOrderDOV2 trfOrder, TrfInfoPO trfInfoPO) {
        if (TrfStatusEnum.New.getStatus() != trfInfoPO.getStatus() && (
                Objects.equals(Integer.parseInt(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.MORE_VS_ONE.getRule())
                        || Objects.equals(Integer.parseInt(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_ONE.getRule())
        )) {
            log.error("The trf not support to bind another order.  OrderNo={}, TrfIdList={}",
                    trfOrder.getOrderNo(), trfInfoPO.getTrfNo());
            throw new BizException("The trf mode cannot be bound to other、Order.");
        }
    }

    public void checkParam(Long trfId, TrfOrderDOV2 trfOrder) {
//        Assert.notNull(trfId);
//        Assert.notNull(trfOrder);
//        Assert.notNull(trfOrder.getSystemId());
//        Assert.notNull(trfOrder.getOrderNo());
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfId,errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfId is null");
        ErrorAssert.notNull(trfOrder,errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfOrder is null");
        ErrorAssert.notNull(trfOrder.getSystemId(),errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), "systemId is null");
        ErrorAssert.notNull(trfOrder.getOrderNo(),errorCode,ResponseCode.ILLEGAL_ARGUMENT.getCode(), "orderNo is null");
    }

    public void unbind(Long trfId, TrfOrderDOV2 r) {
        checkParam(trfId, r);

        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId)
                .andSystemIdEqualTo(r.getSystemId())
                .andOrderNoEqualTo(r.getOrderNo())
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        if (Func.isNotEmpty(r.getOrderId())) {
            criteria.andOrderIdEqualTo(r.getOrderId());
        }
        List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
        if(Func.isEmpty(trfOrderPOList) && Func.isNotEmpty(r.getRealOrderNo())) {
            TrfOrderExample orderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNO = orderExampleByRealOrderNo.createCriteria().
                    andOrderNoEqualTo(r.getRealOrderNo()).
                    andSystemIdEqualTo(r.getSystemId()).
                    andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).
                    andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(r.getOrderId())) {
                criteriaByRealOrderNO.andOrderIdEqualTo(r.getOrderId());
            }
            trfOrderPOList = trfOrderMapper.selectByExample(orderExampleByRealOrderNo);
        }
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(Func.isNotEmpty(trfOrderPOList), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order not found");
        //Assert.isTrue(Func.isNotEmpty(trfOrderPOList), ResponseCode.INTERNAL_SERVER_ERROR, "The order not found");

        Date now = DateUtil.now();
        TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOList, 0);
        trfOrderPO.setModifiedBy(USER_DEFAULT);
        trfOrderPO.setModifiedDate(now);
        trfOrderPO.setBoundStatus(ActiveIndicatorEnum.Inactive.getStatus());
        // 数据也需要置为无效吗?
        trfOrderPO.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());
        trfOrderMapper.updateByPrimaryKey(trfOrderPO);

        //记录变更日志
        logTrfOrderChanged(Arrays.asList(trfOrderPO));
    }

    public void pending(Long trfId, TrfOrderDOV2 r) {
        checkParam(trfId, r);

        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId)
                .andSystemIdEqualTo(r.getSystemId())
                .andOrderNoEqualTo(r.getOrderNo())
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        if (Func.isNotEmpty(r.getOrderId())) {
            criteria.andOrderIdEqualTo(r.getOrderId());
        }
        List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
        if(Func.isEmpty(trfOrderPOList) && Func.isNotEmpty(r.getRealOrderNo())) {
            TrfOrderExample orderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNO = orderExampleByRealOrderNo.createCriteria().
                    andOrderNoEqualTo(r.getRealOrderNo()).
                    andSystemIdEqualTo(r.getSystemId()).
                    andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).
                    andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(r.getOrderId())) {
                criteriaByRealOrderNO.andOrderIdEqualTo(r.getOrderId());
            }
            trfOrderPOList = trfOrderMapper.selectByExample(orderExampleByRealOrderNo);
        }
        //Assert.isTrue(Func.isNotEmpty(trfOrderPOList), ResponseCode.INTERNAL_SERVER_ERROR, "The order not found");
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(Func.isNotEmpty(trfOrderPOList), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order not found");
        Date now = DateUtil.now();
        TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOList, 0);
        trfOrderPO.setModifiedBy(USER_DEFAULT);
        trfOrderPO.setModifiedDate(now);
        trfOrderPO.setPendingFlag(PendingFlagEnum.Pending.getType());
        trfOrderPO.setPendingType(r.getPendingType());
        trfOrderPO.setPendingRemark(r.getPendingRemark());

        trfOrderMapper.updateByPrimaryKey(trfOrderPO);

        //记录变更日志
        logTrfOrderChanged(Arrays.asList(trfOrderPO));
    }

    public void unPending(Long trfId, TrfOrderDOV2 r) {
        checkParam(trfId, r);
//        Assert.notNull(r.getOrderStatus());

        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId)
                .andSystemIdEqualTo(r.getSystemId())
                .andOrderNoEqualTo(r.getOrderNo())
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andPendingFlagEqualTo(PendingFlagEnum.Pending.getType())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        if (Func.isNotEmpty(r.getOrderId())) {
            criteria.andOrderIdEqualTo(r.getOrderId());
        }
        List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
        if (Func.isEmpty(trfOrderPOList) && Func.isNotEmpty(r.getRealOrderNo())) {
            TrfOrderExample orderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNO = orderExampleByRealOrderNo.createCriteria().
                    andOrderNoEqualTo(r.getRealOrderNo()).
                    andSystemIdEqualTo(r.getSystemId()).
                    andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).
                    andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(r.getOrderId())) {
                criteriaByRealOrderNO.andOrderIdEqualTo(r.getOrderId());
            }
            trfOrderPOList = trfOrderMapper.selectByExample(orderExampleByRealOrderNo);
        }
        //Assert.isTrue(Func.isNotEmpty(trfOrderPOList), ResponseCode.INTERNAL_SERVER_ERROR, "The order not found");
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(Func.isNotEmpty(trfOrderPOList), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order not found");
        Date now = DateUtil.now();
        TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOList, 0);
        trfOrderPO.setModifiedBy(USER_DEFAULT);
        trfOrderPO.setModifiedDate(now);
        trfOrderPO.setPendingFlag(PendingFlagEnum.UnPending.getType());
        trfOrderPO.setPendingType(r.getPendingType());
        trfOrderPO.setPendingRemark(r.getPendingRemark());

        trfOrderMapper.updateByPrimaryKey(trfOrderPO);

        //记录变更日志
        logTrfOrderChanged(Arrays.asList(trfOrderPO));

    }

    public void saveChildOrderList(Integer systemId, String orderNo, String orderId, List<TrfChildOrderDO> childOrderList) {
        if (Func.isEmpty(childOrderList) || Func.isEmpty(orderNo) || Func.isEmpty(systemId)) {
            return;
        }
        // 先删除
        TrfChildOrderExample childOrderExample = new TrfChildOrderExample();
        childOrderExample.createCriteria().andParentOrderNoEqualTo(orderNo).andSystemIdEqualTo(systemId);
        childOrderMapper.deleteByExample(childOrderExample);
        // 插入
        List<TrfChildOrderPO> list = new ArrayList<>();
        childOrderList.forEach(
                l -> {
                    TrfChildOrderPO childOrderPO = new TrfChildOrderPO();
                    childOrderPO.setId(idService.nextId());
                    childOrderPO.setSystemId(systemId);
                    childOrderPO.setParentOrderId(orderId);
                    childOrderPO.setParentOrderNo(orderNo);
                    childOrderPO.setOrderNo(l.getOrderNo());
                    childOrderPO.setOrderId(l.getOrderId());
                    list.add(childOrderPO);
                }
        );
        if (Func.isNotEmpty(list)) {
            childOrderMapper.batchInsert(list);
        }
    }

    public void modifyStatus(TrfStatusControlDO statusCtrlDo,Long trfId, TrfOrderDOV2 r) {
        checkParam(trfId, r);
        Assert.notNull(r.getOrderStatus());


        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId)
                .andSystemIdEqualTo(r.getSystemId())
                .andOrderNoEqualTo(r.getOrderNo())
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                //SCI-1468
                .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
        if (Func.isNotEmpty(r.getOrderId())) {
            criteria.andOrderIdEqualTo(r.getOrderId());
        }
        List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
        if (Func.isEmpty(trfOrderPOList) && Func.isNotEmpty(r.getRealOrderNo())) {
            TrfOrderExample trfOrderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNo = trfOrderExampleByRealOrderNo.createCriteria();
            criteriaByRealOrderNo.andTrfIdEqualTo(trfId)
                    .andSystemIdEqualTo(r.getSystemId())
                    .andOrderNoEqualTo(r.getRealOrderNo())
                    .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                    .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                    //SCI-1468
                    .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(r.getOrderId())) {
                criteriaByRealOrderNo.andOrderIdEqualTo(r.getOrderId());
            }
            trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExampleByRealOrderNo);
        }
        // SCI-1683
        if(Func.isEmpty(trfOrderPOList) && Objects.equals(statusCtrlDo.getSystemId(), SYSTEM_SGSMART) && Objects.equals(r.getSystemId(), SgsSystem.GPO.getSgsSystemId())) {
            TrfOrderExample trfOrderExampleByRealOrderNo = new TrfOrderExample();
            TrfOrderExample.Criteria criteriaByRealOrderNo = trfOrderExampleByRealOrderNo.createCriteria();
            criteriaByRealOrderNo.andTrfIdEqualTo(trfId)
                    .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                    .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus())
                    //SCI-1468
                    .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
            if (Func.isNotEmpty(r.getOrderId())) {
                criteriaByRealOrderNo.andOrderIdEqualTo(r.getOrderId());
            }
            trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExampleByRealOrderNo).stream().filter(orderPO -> Objects.equals(orderPO.getEnquiryNo(),r.getOrderNo())).collect(Collectors.toList());
        }
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFORDERDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(Func.isNotEmpty(trfOrderPOList), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The order not found");

        Date now = DateUtil.now();
        TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOList, 0);
        trfOrderPO.setModifiedBy(USER_DEFAULT);
        trfOrderPO.setModifiedDate(now);
        trfOrderPO.setOrderStatus(r.getOrderStatus());
        trfOrderPO.setOrderExpectDueDate(r.getOrderExpectDueDate());
        trfOrderMapper.updateByPrimaryKey(trfOrderPO);

        // 保存childOrder
        saveChildOrderList(r.getSystemId(), r.getOrderNo(), r.getOrderId(), r.getChildOrderList());

        logTrfOrderChanged(Arrays.asList(trfOrderPO));
    }


    public Date getDueDateByTrfId(Long trfId) {
        List<TrfOrderPO> trfOrderPOS = selectOrderListByTrfId(trfId);
        if (Func.isEmpty(trfOrderPOS)) {
            return null;
        }

        return trfOrderPOS.stream().map(TrfOrderPO::getOrderExpectDueDate).filter(Func::isNotEmpty).max(Comparator.comparing(date -> date)).orElse(null);
    }


    public List<TrfOrderDOV2> selectByExample(Long trfId, Integer systemId, String orderNo, String orderId) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());

        if (Func.isNotEmpty(trfId)) {
            criteria.andTrfIdEqualTo(trfId);
        }

        if (Func.isNotEmpty(systemId)) {
            criteria.andSystemIdEqualTo(systemId);
        }

        if (Func.isNotEmpty(orderNo)) {
            criteria.andOrderNoEqualTo(orderNo);
        }

        if (Func.isNotEmpty(orderId)) {
            criteria.andOrderIdEqualTo(orderId);
        }

        return convertTrfOrderDO(trfOrderMapper.selectByExample(trfOrderExample));
    }

    public List<TrfOrderPO> selectActiveByOrderNoList(String trfNo, String orderNo, Integer systemId) {

        return trfOrderExtMapper.selectByTrfNoAndOrderNo(trfNo, orderNo, systemId);
    }

    public List<TrfOrderPO> selectAllByOrderNo(String orderNo, Integer systemId) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andOrderNoEqualTo(orderNo)
                .andSystemIdEqualTo(systemId);
        return trfOrderMapper.selectByExample(trfOrderExample);
    }


    public TrfOrderPO selectOrderInfo(Long trfId, String orderNo, Integer systemId) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus()).andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());

        if (Func.isNotEmpty(trfId)) {
            criteria.andTrfIdEqualTo(trfId);
        }

        if (Func.isNotEmpty(systemId)) {
            criteria.andSystemIdEqualTo(systemId);
        }

        if (Func.isNotEmpty(orderNo)) {
            criteria.andOrderNoEqualTo(orderNo);
        }
        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(trfOrderExample);
        return CollUtil.get(trfOrderPOS, 0);
    }

    public void batchCancel(Long trfId, Integer reasonType, String reasonRemark) {
        Assert.notNull(trfId);

        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(trfId)
                .andOrderStatusNotEqualTo(TrfOrderStatusEnum.Cancelled.getStatus())
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
        if (Func.isEmpty(trfOrderPOList)) {
            return;
        }

        Date now = DateUtil.now();
        for (TrfOrderPO trfOrderPO : trfOrderPOList) {
            trfOrderPO.setCancelType(reasonType);
            trfOrderPO.setCancelRemark(reasonRemark);
            trfOrderPO.setModifiedBy(USER_DEFAULT);
            trfOrderPO.setModifiedDate(now);
            trfOrderPO.setOrderStatus(TrfOrderStatusEnum.Cancelled.getStatus());
        }

        trfOrderMapper.batchUpdate(trfOrderPOList);

        logTrfOrderChanged(trfOrderPOList);
    }


    private List<TrfOrderDOV2> convertTrfOrderDO(List<TrfOrderPO> list) {
        List<TrfOrderDOV2> orderDOV2s = new ArrayList<>();
        if (Func.isNotEmpty(list)) {
            list.forEach(
                    l -> {
                        TrfOrderDOV2 dov2 = new TrfOrderDOV2();
                        dov2.setId(l.getId());
                        dov2.setTrfId(l.getTrfId());
                        dov2.setSystemId(l.getSystemId());
                        dov2.setOrderId(l.getOrderId());
                        dov2.setOrderNo(l.getOrderNo());
                        dov2.setOrderStatus(l.getOrderStatus());
                        dov2.setBoundStatus(l.getBoundStatus());
                        dov2.setPendingType(l.getPendingType());
                        dov2.setPendingFlag(l.getPendingFlag());
                        dov2.setPendingRemark(l.getPendingRemark());
                        dov2.setCancelReasonType(l.getCancelType());
                        dov2.setCancelReasonRemark(l.getCancelRemark());
                        dov2.setOrderExpectDueDate(l.getOrderExpectDueDate());
                        dov2.setEnquiryNo(l.getEnquiryNo());
                        orderDOV2s.add(dov2);
                    }
            );
        }
        return orderDOV2s;
    }

    private void logTrfOrderChanged(List<TrfOrderPO> trfOrderPOList) {
        List<TrfOrderLogPO> batchOrderLogList = new ArrayList<>();
        Date now = DateUtil.now();
        for (TrfOrderPO trfOrderPO : trfOrderPOList) {
            TrfOrderLogPO trfOrderLogPO = new TrfOrderLogPO();
            trfOrderLogPO.setId(idService.nextId());
            Optional.ofNullable(MDC.getCopyOfContextMap()).map(map -> map.get("traceabilityId")).ifPresent(trfOrderLogPO::setRequestId);
//            String requestIdParam = HeaderHelper.getParamValue(TrfLogDomainService.REQUEST_ID);
//            trfOrderLogPO.setRequestId(requestIdParam);
            trfOrderLogPO.setTrfOrderId(trfOrderPO.getId());
            trfOrderLogPO.setTrfId(trfOrderPO.getTrfId());
            trfOrderLogPO.setSystemId(trfOrderPO.getSystemId());
            trfOrderLogPO.setOrderId(trfOrderPO.getOrderId());
            trfOrderLogPO.setOrderNo(trfOrderPO.getOrderNo());
            trfOrderLogPO.setOrderStatus(trfOrderPO.getOrderStatus());
            trfOrderLogPO.setBoundStatus(trfOrderPO.getBoundStatus());
            trfOrderLogPO.setActiveIndicator(trfOrderPO.getActiveIndicator());
            trfOrderLogPO.setPendingType(trfOrderPO.getPendingType());
            trfOrderLogPO.setPendingFlag(trfOrderPO.getPendingFlag());
            trfOrderLogPO.setPendingRemark(trfOrderPO.getPendingRemark());
            trfOrderLogPO.setCancelType(trfOrderPO.getCancelType());
            trfOrderLogPO.setCancelRemark(trfOrderPO.getCancelRemark());
            trfOrderLogPO.setCreatedBy(USER_DEFAULT);
            trfOrderLogPO.setCreatedDate(now);
            trfOrderLogPO.setModifiedBy(USER_DEFAULT);
            trfOrderLogPO.setModifiedDate(now);
            batchOrderLogList.add(trfOrderLogPO);
        }
        trfOrderLogMapper.batchInsert(batchOrderLogList);
    }

    public void updateOrderStatus(String trfNo, Integer refSystemId, int status, Integer pendingFlag) {
        TrfOrderExample orderExample = new TrfOrderExample();
        orderExample.createCriteria()
                .andRefSystemIdEqualTo(refSystemId)
                .andTrfNoEqualTo(trfNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());

        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(orderExample);
        if (Func.isNotEmpty(trfOrderPOS)) {
            trfOrderPOS.forEach(
                    trfOrderPO -> {
                        trfOrderPO.setOrderStatus(status);
                        if (Func.isNotEmpty(pendingFlag)) {
                            trfOrderPO.setPendingFlag(pendingFlag);
                        }
                        trfOrderMapper.updateByPrimaryKey(trfOrderPO);
                    }
            );
        }
    }

    public void updateOrderStatus(String trfNo, String orderNo, Integer refSystemId, int status, Integer pendingFlag) {
        TrfOrderExample orderExample = new TrfOrderExample();
        orderExample.createCriteria()
                .andRefSystemIdEqualTo(refSystemId)
                .andTrfNoEqualTo(trfNo)
                .andOrderNoEqualTo(orderNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());

        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(orderExample);
        if (Func.isNotEmpty(trfOrderPOS)) {
            TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOS, 0);
            trfOrderPO.setOrderStatus(status);
            if (Func.isNotEmpty(pendingFlag)) {
                trfOrderPO.setPendingFlag(pendingFlag);
            }
            trfOrderMapper.updateByPrimaryKey(trfOrderPO);
        }
    }

    public TrfOrderPO selectOrderInfoByParams(String trfNo, String orderNo, String realOrderNo, Integer refSystemId) {
        TrfOrderExample orderExample = new TrfOrderExample();
        orderExample.createCriteria()
                .andRefSystemIdEqualTo(refSystemId)
                .andTrfNoEqualTo(trfNo)
                .andOrderNoEqualTo(orderNo)
                .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        TrfOrderPO trfOrderPO = CollUtil.get(trfOrderMapper.selectByExample(orderExample), 0);
        if(Func.isEmpty(trfOrderPO) && Func.isNotEmpty(realOrderNo)) {
            TrfOrderExample orderExampleByRealOrderNo = new TrfOrderExample();
            orderExampleByRealOrderNo.createCriteria()
                    .andRefSystemIdEqualTo(refSystemId)
                    .andTrfNoEqualTo(trfNo)
                    .andOrderNoEqualTo(realOrderNo)
                    .andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
            return CollUtil.get(trfOrderMapper.selectByExample(orderExampleByRealOrderNo), 0);
        }
        return trfOrderPO;
    }

    public List<Map<String, String>> selectChildOrderListByOrderNo(String orderNo, Integer systemId) {
        List<Map<String, String>> mapList = new ArrayList<>();
        if (Func.isEmpty(systemId) || Func.isBlank(orderNo)) {
            return mapList;
        }
        List<String> stringList = new ArrayList<>();
        TrfChildOrderExample example = new TrfChildOrderExample();
        example.createCriteria().andParentOrderNoEqualTo(orderNo).andSystemIdEqualTo(systemId);
        List<TrfChildOrderPO> list = trfChildOrderMapper.selectByExample(example);
        if (Func.isNotEmpty(list)) {
            list.forEach(
                    l -> {
                        Map<String, String> map = new HashMap<>();
                        map.put(ORDER_ID, l.getOrderId());
                        map.put(ORDER_NO, l.getOrderNo());
                        String key = l.getOrderId() + l.getOrderNo();
                        if (!stringList.contains(key)) {
                            stringList.add(key);
                            mapList.add(map);
                        }
                    }
            );
        }
        return mapList;
    }

    public void updateUnBindStatusByTrfId(Long trfId, String orderNo) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        if (Func.isNotBlank(orderNo)) {
            criteria.andOrderNoEqualTo(orderNo);
        }
        criteria.andTrfIdEqualTo(trfId);
        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(trfOrderExample);
        trfOrderPOS.forEach(l -> l.setBoundStatus(ActiveIndicatorEnum.Inactive.getStatus()));
        trfOrderMapper.batchUpdate(trfOrderPOS);

    }

    public void updateOrderNoByEnquiryNo(String orderId,String enquiryNo, String orderNo, String trfNo) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andEnquiryNoEqualTo(enquiryNo);
        criteria.andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        criteria.andTrfNoEqualTo(trfNo);
        List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(trfOrderExample);
        if (Func.isEmpty(trfOrderPOS)) {
            return;
        }
        TrfOrderPO trfOrderPO = trfOrderPOS.get(0);
        trfOrderPO.setOrderNo(orderNo);
        trfOrderPO.setOrderId(orderId);
        trfOrderPO.setModifiedDate(new Date());
        trfOrderMapper.updateByPrimaryKey(trfOrderPO);
    }

    public void updateOrderActiveFlag(Long id) {
        TrfOrderExample trfOrderExample = new TrfOrderExample();
        TrfOrderExample.Criteria criteria = trfOrderExample.createCriteria();
        criteria.andTrfIdEqualTo(id);
        trfOrderMapper.deleteByExample(trfOrderExample);
    }
}
