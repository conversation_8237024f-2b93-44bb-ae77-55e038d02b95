package com.sgs.customerbiz.biz.service.importtrf.cmd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.sgs.config.api.service.ConfigService;
import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.customertrf.service.CustomerTrfDomainService;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.service.SgsMartService;
import com.sgs.customerbiz.biz.service.importtrf.AfterConvertProcessor;
import com.sgs.customerbiz.biz.service.importtrf.ImportTrfActionExtPt;
import com.sgs.customerbiz.biz.service.todolist.TodoListService;
import com.sgs.customerbiz.biz.utils.CustomerContentUtils;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.core.util.SpringUtil;
import com.sgs.customerbiz.core.util.UserHelper;
import com.sgs.customerbiz.domain.domainevent.TrfNewEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.facade.model.req.QueryTestLineMappingReq;
import com.sgs.customerbiz.facade.model.rsp.QueryTestLineMappingRsp;
import com.sgs.customerbiz.facade.model.rsp.ReturnListRsp;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.customerbiz.model.ext.dto.req.MappingTestLineReq;
import com.sgs.customerbiz.model.ext.dto.req.NewCheckTestLineMappingReq;
import com.sgs.customerbiz.model.ext.dto.rsp.NewCheckTestLineMappingRsp;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.GetConfigReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.CustomerUsage;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ObjectUtil;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductSampleAttrDTO;
import com.sgs.trimslocal.facade.IPpFacade;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.req.GetPpTestLineInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpTestLineInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.*;
import static com.sgs.framework.model.enums.RefSystemIdEnum.Shein;
import static com.sgs.framework.model.enums.RefSystemIdEnum.SheinSupplier;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/5 18:09
 */
@Service
@Slf4j
@Extension
public class DefaultImportTrfActionExtPt implements ImportTrfActionExtPt {

    @Resource
    private TrfDomainService trfDomainService;

    @Resource
    protected CustomerTrfDomainService customerTrfDomainService;

    @Autowired
    protected DataConvertor<String, String, JSON> jsonDataConvertor;

    @Resource
    protected ApplicationEventPublisher applicationEventPublisher;

    @Resource
    protected FrameWorkClient frameWorkClient;

    @Autowired
    protected TodoListService todoListService;

    @Autowired
    private LocalILayerClient localILayerClient;

    @Resource
    private IPpFacade iPpFacade;

    @Autowired
    protected ConfigService configService;

    @Autowired
    protected SgsMartService sgsMartService;

    @Autowired
    private DffClient dffClient;

    @Autowired
    private ConfigClient configClient;

    @Autowired
    private AfterConvertProcessor afterConvertProcessor;

    @Override
    public TrfImportResult importTrf(TrfImportReq importReq) {

        // getCustomerTrf
        String customerTrfJson = getCustomerTrf(importReq);

        checkData(importReq.getRefSystemId(), JSONObject.parseObject(customerTrfJson), importReq.getTrfNo());

        // ConvertTo SgsTrf 结构
        TrfDTO sgsTrf = convert(customerTrfJson, importReq);

        //后置处理
        afterConvert(sgsTrf, importReq);

        // createSgsTrf
        createSgsTrf(sgsTrf, customerTrfJson, importReq);

        //ext after import
        postImoprtExt(sgsTrf, importReq);

        return JSONObject.parseObject(JSONObject.toJSONString(sgsTrf), TrfImportResult.class);
    }

    public void afterConvert(TrfDTO sgsTrf, TrfImportReq importReq) {
        sgsTrf.getHeader().setSystemId(importReq.getSystemId());
        if (Func.isNotBlank(importReq.getTrfTemplateId()) && Func.isBlank(sgsTrf.getHeader().getTrfTemplateId())) {
            sgsTrf.getHeader().setTrfTemplateId(importReq.getTrfTemplateId());
        }
        afterConvertProcessor.processDffCodeMapping(importReq.getRefSystemId(), importReq.getFormId(),sgsTrf);
        if (Func.isNotBlank(importReq.getCustomerTrfId())) {
            sgsTrf.getHeader().setCustomerTrfId(importReq.getCustomerTrfId());
        }

    }

    protected static final String RELATIONSHIP_LIST_PATH = "$.relationShipList[relType=1][0].systemId";

    protected void checkData(Integer refSystemId, JSONObject data, String trfNo) {

    }

    protected boolean isEmpty(JSONObject jsonObject) {
        return jsonObject == null || jsonObject.isEmpty();
    }

    protected Optional<Integer> getSystemIdFromJson(JSONObject jsonObject, String jsonPath) {
        try {
            Object result = JSONPath.eval(jsonObject, jsonPath);
            if (Func.isEmpty(result)) {
                return Optional.empty();
            }
            String systemIdStr = result.toString();
            return StringUtils.isEmpty(systemIdStr)
                    ? Optional.empty()
                    : Optional.of(Integer.parseInt(systemIdStr));
        } catch (NumberFormatException e) {
            return Optional.empty();
        }
    }

    protected String buildErrorMessage(String trfNo, Integer expectedSystemId, Integer actualSystemId) {
        return StrUtil.format("Trf No: {} 属于{}的Trf，无法在当前页面import! (Expected: {}, Actual: {})",
                trfNo, RefSystemIdEnum.getRefSystemId(actualSystemId).getName(), expectedSystemId, actualSystemId);
    }

    public String getCustomerTrf(TrfImportReq importReq) {
        return customerTrfDomainService.getCustomerTrf(importReq);
    }

    public TrfDTO convert(String customerTrfJson, TrfImportReq importReq) {
        //CustomerTrf Json 转换为SgsTrf结构
        TrfDTO trfDTO = customerTrfDomainService.convert(customerTrfJson,
                importReq.getRefSystemId(), importReq.getSystemId(), importReq.getLabCode(), importReq.getBuCode(),
                importReq.getTrfTemplateId(),importReq.getTrfTemplateType(),
                importReq.getFormId(), importReq.getGridId(), importReq.getLabContact());

        // createTrfByCustomerTrf 则trf.source = TrfToOrder
        trfDTO.getHeader().setSource(TrfSourceType.TRF2Order.getSourceType());

        // 初始化Trf.Header.Lab 信息
        initTrfLab(trfDTO, importReq);

        // TL Mapping    customer TL -> SGS TL
        tlMapping(trfDTO, importReq);

        return trfDTO;
    }

    public void tlMapping(TrfDTO trfDTO, TrfImportReq importReq) {
        List<TrfTestItemDTO> testLineList = trfDTO.getTestLineList();
        if (Func.isEmpty(testLineList)) {
            return;
        }
        QueryTestLineMappingReq checkTestLineMappingReq = new QueryTestLineMappingReq();
        List<String> customerGroupCodeByRefSystemId = configService.getCustomerGroupCodeByRefSystemId(importReq.getRefSystemId());
        if (Func.isNotEmpty(customerGroupCodeByRefSystemId)) {
            checkTestLineMappingReq.setCustomerGroupCode(customerGroupCodeByRefSystemId.get(0));
        }
        checkTestLineMappingReq.setProductLineCode(importReq.getProductLineCode());
        checkTestLineMappingReq.setPageIndex(1);
        checkTestLineMappingReq.setPageSize(1);

        List<TrfTestItemDTO> testLineListNew = new ArrayList<>();
        testLineList.forEach(
                l -> {
                    TrfTestItemExternalDTO externalInfo = l.getExternalInfo();
                    if (Func.isNotEmpty(externalInfo)) {
                        Integer checkType = externalInfo.getCheckType();
                        String testItemId = externalInfo.getTestItemId();
                        String testItemName = externalInfo.getTestItemName();
                        checkTestLineMappingReq.setTestCode(testItemId);
                        checkTestLineMappingReq.setTestName(testItemName);
                        CustomResult<QueryTestLineMappingRsp> queryTestLineMappingRspCustomResult = localILayerClient.queryTestLineMapping(checkTestLineMappingReq);
                        if (Func.isNotEmpty(queryTestLineMappingRspCustomResult) && Func.isNotEmpty(queryTestLineMappingRspCustomResult.getData()) && queryTestLineMappingRspCustomResult.isSuccess()) {
                            QueryTestLineMappingRsp data = queryTestLineMappingRspCustomResult.getData();
                            List<ReturnListRsp> returnList = data.getReturnList();
                            if (Func.isNotEmpty(returnList)) {
                                List<ReturnListRsp> list = returnList.stream().filter(v -> Objects.equals(v.getCheckType(), Func.toStr(checkType))).collect(Collectors.toList());
                                if (Func.isNotEmpty(list)) {
                                    ReturnListRsp res = list.get(0);

                                    TrfTestItemDTO testItemDTO = new TrfTestItemDTO();
                                    BeanUtil.copy(l, testItemDTO);
                                    testItemDTO.setTestLineId(Func.toInteger(res.getTestlineId()));
                                    TrfCitationDTO citationDTO = new TrfCitationDTO();
                                    citationDTO.setCitationId(Func.toInteger(res.getCitationId()));
                                    citationDTO.setCitationType(Func.toInteger(res.getCitationType()));
                                    citationDTO.setCitationName(res.getCitationName());
                                    testItemDTO.setCitation(citationDTO);

                                    TrfPpTestLineDTO testLineDTO = new TrfPpTestLineDTO();
                                    testLineDTO.setPpNo(Func.toInteger(res.getPpNo()));
                                    testLineDTO.setPpName(res.getPpName());
                                    testItemDTO.setPpTestLineRelList(Arrays.asList(testLineDTO));
                                    testLineListNew.add(testItemDTO);
                                }
                            }
                        }
                    }
                }
        );
        if (Func.isNotEmpty(testLineListNew)) {
            trfDTO.setTestLineList(testLineListNew);
        }
    }

    public void initTrfLab(TrfDTO trfDTO, TrfImportReq importReq) {
        // 如果请求参数里没有指定Lab 信息，则不考虑初始化
        if (StringUtil.isBlank(importReq.getLabCode())) {
            return;
        }

        // 如果客户请求参数已经指定Lab信息，则也不考虑替换 （校验逻辑在后面判定，不在此处处理）
        if (ObjectUtil.isNotEmpty(trfDTO.getHeader().getLab()) && Func.isNotEmpty(trfDTO.getHeader().getLab().getLabCode())) {
            return;
        }

        // 只有importReq里面指定了lab 且 客户没有传入，则将请求实验室信息初始化到Trf
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(importReq.getLabCode());
        if (ObjectUtil.isEmpty(labInfo)) {
            log.error("The Lab not found by labCode : {}. ", importReq.getLabCode());
            return;
        }

        // 客户没有传入Lab，将请求实验室信息初始化到Trf
        TrfLabDTO trfLabDTO = convertLab(labInfo, importReq.getProductLineCode(), importReq.getLabContactName());

        trfDTO.getHeader().setLab(trfLabDTO);
    }

    private TrfLabDTO convertLab(LabInfo labInfo, String buCode,String labContactName) {
        TrfLabDTO trfLabDTO = new TrfLabDTO();

        trfLabDTO.setLabCode(labInfo.getLaboratoryCode());
        trfLabDTO.setLabName(labInfo.getLaboratoryName());
        trfLabDTO.setBuCode(buCode);
        trfLabDTO.setLabId(Func.toLong(labInfo.getLaboratoryID()));
        if (Func.isNotBlank(labContactName)) {
            TrfLabContactDTO labContact = trfLabDTO.getLabContact();
            if (Func.isEmpty(labContact)) {
                labContact = new TrfLabContactDTO();
            }
            labContact.setContactName(labContactName);
            trfLabDTO.setLabContact(labContact);
        }
        return trfLabDTO;
    }

//    protected TrfDTO createTrfByCustomerTrf(String customerTrfJson, TrfImportReq importReq) {
//        // ConvertTo SgsTrf 结构
//        TrfDTO sgsTrf = convert(customerTrfJson, importReq);
//
//        // createTrfByCustomerTrf 则trf.source = TrfToOrder
//        sgsTrf.getHeader().setSource(TrfSourceType.TRF2Order.getSourceType());
//
//        // createSgsTrf
//        createSgsTrf(sgsTrf, customerTrfJson);
//
//        return sgsTrf;
//    }

    protected void postImoprtExt(TrfDTO trfDTO, TrfImportReq importReq) {
        // 在Import成功后，默认抛出一个TRF_CREATE_EVENT
        publishTrfCreateEvent(trfDTO, importReq);
    }

    protected void publishTrfCreateEvent(TrfDTO trfDTO, TrfImportReq importReq) {
        TrfNewEvent createEvent = getTrfNewEvent(trfDTO, importReq);

        applicationEventPublisher.publishEvent(createEvent);
    }

    public TrfNewEvent getTrfNewEvent(TrfDTO trfDTO, TrfImportReq importReq) {
        TrfNewEvent createEvent = new TrfNewEvent(TrfEventTriggerBy.CUSTOMER);

        createEvent.setPayload(trfDTO);
        createEvent.setRefSystemId(importReq.getRefSystemId());
        createEvent.setProductLineCode(Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? importReq.getProductLineCode() : ProductLineContextHolder.getProductLineCode());
        createEvent.setSystemId(importReq.getSystemId());
        createEvent.setSourceId(importReq.getRequestId());
        createEvent.setTrfNo(importReq.getTrfNo());
        createEvent.setStatusFrom(TrfStatusEnum.New.getStatus());
        createEvent.setStatusTo(TrfStatusEnum.New.getStatus());
        createEvent.setOperator(importReq.getOperator());
        createEvent.setOperationTime(importReq.getOperationTime());
        return createEvent;
    }

    private TrfDTO createSgsTrf(TrfDTO sgsTRF, String customerTRFJson, TrfImportReq importReq) {
        // 1.validate trf
        validateImportTrf(sgsTRF, importReq);
        preHandler(sgsTRF, importReq);
        TrfDOV2 trfParam = TrfConvertor.toTrfDOV2(sgsTRF);

        beforeCreateTrf(trfParam, sgsTRF, importReq);

        //todo todolist中的双写保存逻辑接口后续也需要重构，本次重构importTRF暂时不动
        this.saveTodolistTRF(sgsTRF, customerTRFJson, importReq, needLabCode());
        // 2.save SCI TRF
        trfDomainService.createTrf(trfParam);
        return sgsTRF;
    }

    public void beforeCreateTrf(TrfDOV2 trfParam, TrfDTO sgsTRF, TrfImportReq importReq) {
        Integer refSystemId = importReq.getRefSystemId();
        if (Objects.nonNull(refSystemId) && RefSystemIdEnum.TARGET_INSPECTORIO.getRefSystemId() == refSystemId) {
            String sgsTrfNo = sgsMartService.createSgsmartTrfByCustomerTrf(sgsTRF, importReq);
            Optional.ofNullable(trfParam).map(TrfDOV2::getHeader).ifPresent(h -> h.setSgsTrfNo(sgsTrfNo));
        }
    }

    public void preHandler(TrfDTO sgsTRF, TrfImportReq importReq) {

    }

    public void validateImportTrf(TrfDTO sgsTRF, TrfImportReq importReq) {
        TrfLabDTO importTrfLab = sgsTRF.getHeader().getLab();
        if (StringUtil.isNotBlank(importTrfLab.getLabCode())) {
            String currentLabCode = UserHelper.getLocalUser() == null ? null : UserHelper.getLocalUser().getCurrentLabCode();
            currentLabCode = StringUtils.defaultIfEmpty(ProductLineContextHolder.getLabCode(), currentLabCode);
            if (!Objects.equals(currentLabCode, importTrfLab.getLabCode())) {
                log.error("n not import this trf ,currentLab:{},customer lab:{}", currentLabCode, importTrfLab.getLabCode());
            }
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.DAFAULTIMPORTTRFACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
            ErrorAssert.isTrue(Objects.equals(currentLabCode, importTrfLab.getLabCode()), errorCode, ResponseCode.FAIL.getCode(), "The customer trf lab and system lab are not match，can not import this trf");
        }
    }

    public void saveTodolistTRF(TrfDTO sgsTRF, String customerTRFJson, TrfImportReq importReq, boolean needLabCode) {
        // todolist保存需要sgsToken，沿用原来逻辑设置默认的user
        if (UserHelper.getLocalUser() == null) {
            UserInfo userInfo = new UserInfo();
            userInfo.setRegionAccount(Func.isNotBlank(importReq.getOperator()) ? importReq.getOperator() : USER_DEFAULT);
            userInfo.setName(Func.isNotBlank(importReq.getOperator()) ? importReq.getOperator() : USER_DEFAULT);
            userInfo.setCurrentLabCode(ProductLineContextHolder.getLabCode());
            UserHelper.setLocalUser(userInfo);
        } else {
            UserInfo localUser = UserHelper.getLocalUser();
            //SCI-1447 labCode of header first
            localUser.setCurrentLabCode(Func.isNotBlank(ProductLineContextHolder.getLabCode()) ? ProductLineContextHolder.getLabCode() : localUser.getCurrentLabCode());
        }

        // 保存todolist trf
        JSONObject customerTrfContent = new JSONObject();
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(importReq.getRefSystemId());
        String trfNo = "";
        switch (refSystem) {
            case Shein:
            case SheinSupplier:
                trfNo = importReq.getTrfNo();
                break;
            case UNIQLO:
                String buyer = Optional.ofNullable(sgsTRF.getCustomerList())
                        .flatMap(cl -> cl.stream().filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Buyer.getUsage())).findFirst())
                        .map(TrfCustomerDTO::getCustomerName)
                        .orElse("");
                JSONObject setBuyer = JSON.parseObject(customerTRFJson);
                setBuyer.put("buyer", buyer);
                customerTRFJson = setBuyer.toJSONString();
            case LOWES:
                trfNo = sgsTRF.getHeader().getTrfNo();
                if (Func.isNotBlank(customerTRFJson) && Func.isNotEmpty(sgsTRF.getHeader()) && Func.isNotEmpty(sgsTRF.getHeader().getLab())) {
                    JSONObject jsonObject = JSON.parseObject(customerTRFJson);
                    jsonObject.put(SCI_COMMON_HEADER_LAB_CODE, sgsTRF.getHeader().getLab().getLabCode());
                    customerTRFJson = jsonObject.toJSONString();
                    break;
                }
            case TARGET_INSPECTORIO:
                trfNo = sgsTRF.getHeader().getTrfNo();
                if(Func.isNotBlank(customerTRFJson)) {
                    customerTRFJson = CustomerContentUtils.mixCustomerTrf(importReq, sgsTRF, customerTRFJson);
                }
                break;
            default:
                trfNo = sgsTRF.getHeader().getTrfNo();
        }
        customerTrfContent.put(Constants.DETAIL, JSON.parseObject(customerTRFJson));
        CustomResult customResult = todoListService.importTrfInfoData(trfNo, sgsTRF.getHeader().getRefSystemId(), customerTrfContent, needLabCode);
        boolean success = customResult.isSuccess();
        if (!success) {
            throw new BizException(customResult.getMsg());
        }
    }

    protected boolean needLabCode() {
        return Boolean.TRUE;
    }
//
//    @PostConstruct
//    public void initCustomerTrfInvokerMap() {
//        customerTrfInvokerList.forEach(customerTrfInvoker -> customerTrfInvokerMap.put(customerTrfInvoker.getName(), customerTrfInvoker));
//    }
//
//    protected String getCustomerTrfImportConvertTemplate(Integer refSystemId) {
//        GetConfigReq getConfigReq = new GetConfigReq();
//        getConfigReq.setConfigKey(ConfigKeyConstant.CONFIG_CUSTOMERTRF_IMPORT_CONVERT_TEMPLATE_TO_SGSTRF);
//        getConfigReq.setRefSystemId(String.valueOf(refSystemId));
//
//        return getConfig(getConfigReq);
//    }
//
//    protected String getOrderImportConvertTemplate(Integer systemId) {
//        GetConfigReq getConfigReq = new GetConfigReq();
//        getConfigReq.setConfigKey(ConfigKeyConstant.CONFIG_CUSTOMERTRF_IMPORT_CONVERT_TEMPLATE_TO_SGSTRF);
//        getConfigReq.setRefSystemId(String.valueOf(systemId));
//
//        return getConfig(getConfigReq);
//    }

}
