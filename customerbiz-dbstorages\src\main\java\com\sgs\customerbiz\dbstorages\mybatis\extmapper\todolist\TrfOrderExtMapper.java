package com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.TrfOrderStatusDto;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> walley.wang
 * @date         : 2023-08-26 23:08
 * @version      : V1.0.0
 * @desc         : TODO 
 */
public interface TrfOrderExtMapper {

    List<TrfOrderPO> pageableQueryOrderList(@Param("minId") Long minId, @Param("offset") Integer offset);

    int batchUpdateOrder(@Param("orderStatusList") List<TrfOrderStatusDto> orderList);

    List<TrfOrderPO> selectByTrfNoAndOrderNo(@Param("trfNo") String trfNo, @Param("orderNo") String orderNo, @Param("systemId") Integer systemId);
}
