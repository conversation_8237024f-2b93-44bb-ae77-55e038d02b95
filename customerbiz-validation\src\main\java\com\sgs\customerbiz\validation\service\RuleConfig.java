package com.sgs.customerbiz.validation.service;

import com.google.common.collect.ImmutableList;
import com.sgs.customerbiz.validation.validator.*;
import com.sgs.customerbiz.validation.validator.target.TargetLabInfo;
import com.sgs.customerbiz.validation.validator.target.TargetProtocol;
import com.sgs.customerbiz.validation.validator.veyer.VeyerSupplier;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class RuleConfig {

    private String ruleCode;
    private String messageTemplate;

    public static RuleConfig of(String ruleCode, String messageTemplate) {
        RuleConfig ruleConfig =  new RuleConfig();
        ruleConfig.setRuleCode(ruleCode);
        ruleConfig.setMessageTemplate(messageTemplate);
        return ruleConfig;
    }

    private static List<RuleConfig> ruleConfigList = ImmutableList.<RuleConfig>builder()
            .add(
                    RuleConfig.of(Required.class.getSimpleName(), "Report Number: ${reportNo}, Missing ${fieldName}")
            )
            .add(
                    RuleConfig.of(ConditionalRequired.class.getSimpleName(), "Report Number: ${reportNo}, Missing ${fieldName} ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(CheckedType.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} should be ${fieldType}")
            )
            .add(
                    RuleConfig.of(MaxLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} exceeds max length ${fieldLength}")
            )
            .add(
                    RuleConfig.of(Exists.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} invalid value")
            )
            .add(
                    RuleConfig.of(ExistsCustomerData.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} invalid value")
            )
            .add(
                    RuleConfig.of(ExistsMapping.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} invalid value")
            )
            .add(
                    RuleConfig.of(StringMatches.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} can only consist of ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(Contains.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} need to contain ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(NonContains.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} cannot contain ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(Between.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} should in ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(Logical.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} should in ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(Length.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be ${fieldLength}")
            )
            .add(
                    RuleConfig.of(SplitLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be ${fieldLength}")
            )
            .add(
                    RuleConfig.of(RangeLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be >=${fieldLength} and < ${fieldLength2}")
            )
            .add(
                    RuleConfig.of(RangeClosedLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be >=${fieldLength} and <= ${fieldLength2}")
            )
            .add(
                    RuleConfig.of(SplitRangeLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be >=${fieldLength} and < ${fieldLength2}")
            )
            .add(
                    RuleConfig.of(SplitRangeClosedLength.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} length must be >=${fieldLength} and <= ${fieldLength2}")
            )
            .add(
                    RuleConfig.of(VeyerSupplier.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} invalid value")
            )
            .add(
                    RuleConfig.of(OnlyOneHaveValue.class.getSimpleName(), "Report Number: ${reportNo}, only one of ${fieldName} and ${otherName} can have a value.")
            )
            .add(
                    RuleConfig.of(ConditionalCheckTestLine.class.getSimpleName(), "Report Number: ${reportNo}, ${ruleDesc} then testLine must contains fail.")
            )
            .add(
                    RuleConfig.of(TargetProtocol.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} invalid value.")
            )
            .add(
                    RuleConfig.of(SplitRequired.class.getSimpleName(), "Report Number: ${reportNo}, Missing ${fieldName}")
            )
            .add(
                    RuleConfig.of(ConditionalWithCustomerTrf.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} must meet  ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(CheckTestMappingExists.class.getSimpleName(), "Report Number: ${reportNo}, testLine not exists in CommonService")
            )
            .add(
                    RuleConfig.of(SimpleScript.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} must meet ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(TargetLabInfo.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} must meet ${ruleDesc}")
            )
            .add(
                    RuleConfig.of(RegexValidator.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} must match the regular expression: ${pattern}")
            )
            .add(
                    RuleConfig.of(ForeignKey.class.getSimpleName(), "Report Number: ${reportNo}, ${fieldName} contains invalid reference ID(s)")
            )
            .build();

    public static RuleConfig findRuleConfig(String ruleCode) {
        Map<String, RuleConfig> ruleConfigMap = ruleConfigList.stream().collect(Collectors.toMap(RuleConfig::getRuleCode, Function.identity()));
        return Optional.ofNullable(ruleConfigMap.get(ruleCode)).orElseThrow(() -> new IllegalArgumentException("Unknown rule code: " + ruleCode));
    }

    public static Optional<RuleConfig> findByRuleCode(String ruleCode) {
            Map<String, RuleConfig> ruleConfigMap = ruleConfigList.stream()
                            .collect(Collectors.toMap(RuleConfig::getRuleCode, Function.identity()));
            return Optional.ofNullable(ruleConfigMap.get(ruleCode));
    }

    public static List<RuleConfig> getRuleConfigList() {
        return ruleConfigList;
    }
}
