package com.sgs.customerbiz.validation.service;

import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSCustomerFieldPO;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSModelFieldPO;
import com.sgs.customerbiz.validation.enums.FieldType;
import lombok.Data;

import java.util.List;

@Data
public class FieldValidationConfig {

    private Integer id;
    private Integer parentId;
    private Integer basicFieldId;
    private String modelName;
    private Integer refSystemId;
    private boolean customized;
    private String expr;
    private String fieldName;
    private String fieldAlias;
    private FieldType fieldType;
    private List<FieldRuleConfig> fieldRuleConfigList;
    private List<FieldValidationConfig> children;

    public boolean notCustomized() {
        return  ! customized;
    }

    public static FieldValidationConfig createFrom(SGSCustomerFieldPO sgsCustomerFieldPO) {
        FieldValidationConfig fieldValidationConfig = new FieldValidationConfig();
        fieldValidationConfig.setRefSystemId(sgsCustomerFieldPO.getRefSystemId());
        fieldValidationConfig.setModelName(sgsCustomerFieldPO.getModelName());
        fieldValidationConfig.setId(sgsCustomerFieldPO.getId());
        fieldValidationConfig.setBasicFieldId(sgsCustomerFieldPO.getFieldId());
        fieldValidationConfig.setCustomized(true);
        fieldValidationConfig.setExpr(sgsCustomerFieldPO.getModelFieldExpr());
        fieldValidationConfig.setFieldName(sgsCustomerFieldPO.getCustomerFieldName());
        fieldValidationConfig.setFieldAlias(sgsCustomerFieldPO.getCustomerFieldAlias());
        fieldValidationConfig.setFieldType(FieldType.lookup(sgsCustomerFieldPO.getFieldType()));
        return fieldValidationConfig;
    }

    public static FieldValidationConfig createFrom(SGSModelFieldPO modelFieldPO) {
        FieldValidationConfig fieldValidationConfig = new FieldValidationConfig();
        fieldValidationConfig.setId(modelFieldPO.getId());
        fieldValidationConfig.setParentId(modelFieldPO.getParentId());
        // todo 这里写死了0是为了判断要不要替换dff使用
        fieldValidationConfig.setRefSystemId(0);
        fieldValidationConfig.setModelName(modelFieldPO.getModelName());
        fieldValidationConfig.setCustomized(false);
        fieldValidationConfig.setExpr(modelFieldPO.getFieldExpr());
        fieldValidationConfig.setFieldName(modelFieldPO.getFieldName());
        fieldValidationConfig.setFieldType(FieldType.lookup(modelFieldPO.getFieldType()));
        return fieldValidationConfig;
    }

}
