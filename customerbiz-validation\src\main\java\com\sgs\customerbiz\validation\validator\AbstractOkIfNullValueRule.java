package com.sgs.customerbiz.validation.validator;

import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import com.sgs.customerbiz.validation.service.ValidationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

public abstract class AbstractOkIfNullValueRule extends AbstractParameterizedRule{

    @Override
    protected ValidationResult doValidate(Object value,
                                          Map<String, Object> params,
                                          FieldValidationConfig fieldValidationConfig,
                                          FieldRuleConfig ruleConfig) {
        if(value == null){
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        if(value instanceof String && StringUtils.isBlank((String) value)){
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        if(value instanceof Collection && ValidationUtils.isEmptyCollection((Collection<?>) value)) {
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        return doValidateIfValueNotNull(value, params, fieldValidationConfig, ruleConfig);
    }

    protected abstract ValidationResult doValidateIfValueNotNull(Object value,
                                                                 Map<String, Object> params,
                                                                 FieldValidationConfig fieldValidationConfig,
                                                                 FieldRuleConfig ruleConfig);


}
