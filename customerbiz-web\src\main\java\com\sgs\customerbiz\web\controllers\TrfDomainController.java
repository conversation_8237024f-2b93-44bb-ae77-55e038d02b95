package com.sgs.customerbiz.web.controllers;

import java.util.List;

import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.web.request.OrderRelationshipQuery;
import com.sgs.framework.core.base.BaseResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.web.request.ReportRelationshipReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "/v2/api/trf")
public class TrfDomainController {

    
    private final TrfReportDomainService trfReportDomainService;

    private final TrfOrderDomainService trfOrderDomainService;

    public TrfDomainController(TrfReportDomainService trfReportDomainService, TrfOrderDomainService trfOrderDomainService) {
        this.trfReportDomainService = trfReportDomainService;
        this.trfOrderDomainService = trfOrderDomainService;
    }

    @PostMapping("/v2/trf/report/relationship")
    @ApiOperation("根据reportNo列表查询报告关系")
    public BaseResponse<List<TrfReportPO>> reportRelationship(@RequestBody ReportRelationshipReq req) {
        return BaseResponse.newSuccessInstance(trfReportDomainService.selectByReportNoList(req.getReportNoList()));
    }

    @PostMapping("/v2/trf/order/relationship")
    @ApiOperation("根据OrderNo列表查询订单关系")
    public BaseResponse<List<TrfOrderPO>> orderRelationship(@Validated @RequestBody OrderRelationshipQuery query) {
        return BaseResponse.of(trfOrderDomainService.selectActiveByOrderNoList(query.getTrfNo(), query.getOrderNo(), query.getSystemId()));
    }

}
