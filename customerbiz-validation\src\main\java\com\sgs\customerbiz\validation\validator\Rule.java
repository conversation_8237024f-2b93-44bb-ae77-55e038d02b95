package com.sgs.customerbiz.validation.validator;

import com.sgs.customerbiz.validation.service.*;
import com.sgs.customerbiz.validation.service.parser.ValueParser;

import java.util.Map;

public interface Rule {

    ValidationResult validate(Object value,
                              Map<String, Object> params,
                              FieldValidationConfig fieldValidationConfig,
                              FieldRuleConfig ruleConfig);

}
