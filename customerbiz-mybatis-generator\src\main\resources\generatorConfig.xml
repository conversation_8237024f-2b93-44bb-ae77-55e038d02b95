<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="SqlServerTables" targetRuntime="MyBatis3" defaultModelType="flat">
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
        一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖-->
        <property name="autoDelimitKeywords" value="true"/>
        <!--beginningDelimiter、endingDelimiter默认为（"），但Mysql中不能这么写，所以要将这两个默认值改为“单反引号（'）”-->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="mapUnderscoreToCamelCase" value="true"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="com.sgs.customerbiz.mybatis.generator.RenameModelPlugin">
            <property name="prefixes2Remove" value="tb,sgs"/>
            <property name="suffix2Append" value="PO"/>
        </plugin>

        <!--自定义批量插入-->
        <plugin type="com.sgs.customerbiz.mybatis.generator.plugin.BatchInsertPlugin"></plugin>
        <!--自定义批量更新-->
        <plugin type="com.sgs.customerbiz.mybatis.generator.plugin.BatchUpdatePlugin"></plugin>

        <!--        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>-->

        <plugin type="com.sgs.customerbiz.mybatis.generator.ForceCreateUpdateTimePlugin">
            <property name="insertTimeColumns" value="create_time"/>
            <property name="lastUpdateTimeColumns" value="modify_time"/>
            <property name="dbCurrentTimeExpr" value="now()"/>
        </plugin>

        <commentGenerator type="com.sgs.customerbiz.mybatis.generator.DBCommentGenerator">
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*********************************************************************************************************************************************"
                        userId="todolist_user"
                        password="SH12y_J398i_P65gH_d19Uy">
            <property name="remarksReporting" value="true"/>
        </jdbcConnection>

        <javaTypeResolver type="com.sgs.customerbiz.mybatis.generator.plugin.MyJavaTypeResolver">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.sgs.customerbiz.dbstorages.mybatis.model"
                            targetProject="customerbiz-dbstorages/src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="sqlmap.autogenerated" targetProject="customerbiz-dbstorages/src/main/resources">
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist"
                             targetProject="customerbiz-dbstorages/src/main/java">
        </javaClientGenerator>


        <!--        <table tableName="tb_cfg_event_subscribe" domainObjectName="EventSubscribe">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <table tableName="tb_trf_report" domainObjectName="TrfReport">
            <property name="mapUnderscoreToCamelCase" value="true"/>
                        <ignoreColumn column="last_modified_timestamp"/>

        </table>
        <table tableName="tb_trf_log" domainObjectName="TrfLog">
            <property name="mapUnderscoreToCamelCase" value="true"/>
            <ignoreColumn column="last_modified_timestamp"/>

        </table>

<!--        <table tableName="tb_api_flow_step" domainObjectName="ProcessFlowStepInfo">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--            <ignoreColumn column="last_modified_timestamp"/>-->

<!--        </table>-->
        <!--        <table tableName="tb_cfg_customer_data" domainObjectName="CfgCustomerData">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <!--        <table tableName="tb_trf_care_label" domainObjectName="trfCareLabel">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <!--        <table tableName="tb_trf_customer" domainObjectName="TrfCustomer">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_customer_contact" domainObjectName="TrfCustomerContact">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_customer_lang" domainObjectName="TrfCustomerLang">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_attachment" domainObjectName="TrfAttachment">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_invoice" domainObjectName="TrfInvoice">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <!--        <table tableName="tb_trf_order" domainObjectName="TrfOrder">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_product" domainObjectName="TrfProduct">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_product_attr" domainObjectName="TrfProductAttr">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_quotation" domainObjectName="TrfQuotation">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_report" domainObjectName="TrfReport">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <!--        <table tableName="tb_trf_service_requirement" domainObjectName="TrfServiceRequirement">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_service_requirement_delivery" domainObjectName="TrfServiceRequirementDelivery">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_service_requirement_lang" domainObjectName="TrfServiceRequirementLang">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_item" domainObjectName="TrfTestItem">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_item_lang" domainObjectName="TrfTestItemLang">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_sample" domainObjectName="TrfTestSample">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_sample_material" domainObjectName="TrfTestSampleMaterial">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_sample_file" domainObjectName="TrfTestSampleFile">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->
        <!--        <table tableName="tb_trf_test_sample_group" domainObjectName="TrfTestSampleGroup">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->

        <!--        </table>-->

        <!--        <table tableName="tb_api_request" domainObjectName="ApiRequest">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->
        <!--        </table>-->

        <!--        <table tableName="tb_trf_order_log" domainObjectName="TrfOrderLog">-->
        <!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
        <!--            <ignoreColumn column="last_modified_timestamp"/>-->
        <!--        </table>-->
    </context>

</generatorConfiguration>
