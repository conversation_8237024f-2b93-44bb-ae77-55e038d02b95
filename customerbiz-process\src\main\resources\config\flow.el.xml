<?xml version="1.0" encoding="UTF-8"?>
<flow>

    <chain name="order2TRF">
        THEN(
            orderToTRFValidateComponent,
            orderToTRFPreHandleComponent,
            refSystemMappingComponent,
            validateTRFExistComponent,
            orderToTrfAttachmentFilter,
            order2TrfConvertComponent,
            SWITCH(orderToTRFRouterComponent).TO(smartOrderToTrfCreateActionComponent.tag("2"),unqloOrderToTrfCreateActionComponent.tag("10017"),defaultOrderToTrfCreateActionComponent).DEFAULT(defaultOrderToTrfCreateActionComponent),
            eventNotifyActionComponent
        );
    </chain>

    <!-- importTRF的smart子流程 -->
    <chain name="SmartImport2TRF" tag="PULL_2">
        THEN(
            smartImportTRFPreHandleComponent,
            smartImportTrfValidateComponent,
            smartImportToTrfSaveDBComponent
        );
    </chain>
    <!-- importTRF的push模式子流程 -->
    <chain name="SubChain_PushImport2TRF" tag="PUSH">
        THEN(
            pushImportGetCustomerActionComponent,
            importToTRFConvertComponent,
            importToTRFPreHandleComponent,
            testLineMappingComponent,
            importToTRFAfterConvertComponent,
            pushImportToTRFCreateActionComponent,
            pushImportToTrfSaveDBComponent,
            pushEventNotifyActionComponent
        );
    </chain>
    <chain name="SubChain_PullImportTRF" tag="PULL">
        SWITCH(importToTRFRouterComponent).TO(
            PullImport2TRF.tag("PULL"),
            SmartImport2TRF.tag("PULL_2")).DEFAULT(PullImport2TRF.tag("default"));
    </chain>
    <!-- importTRF的pull模式子流程 -->
    <chain name="PullImport2TRF" tag="PULL">
        THEN(
            getCustomerTRFComponent,
            pullImportTrfConvertComponent,
            importToTRFPreHandleComponent,
            testLineMappingComponent,
            importToTRFAfterConvertComponent,
            importToTRFCreateActionComponent,
            importToTrfSaveDBComponent,
            eventNotifyActionComponent
        );
    </chain>
    <!-- importTRF的模式子流程 -->
    <chain name="Import2TRF" tag="default">
        THEN(
        getCustomerTRFComponent,
        importToTRFConvertComponent,
        importToTRFPreHandleComponent,
        testLineMappingComponent,
        importToTRFAfterConvertComponent,
        importToTRFCreateActionComponent,
        importToTrfSaveDBComponent,
        eventNotifyActionComponent
        );
    </chain>

    <!-- CustomerTRFInfo的主流程 -->
    <chain name="customerTRFInfo">
        THEN(
            customerTrfQueryActionComponent
        );
    </chain>

    <!-- importTRF的主流程 -->
    <chain name="Import2TRF_Main">
        THEN(
            importToTRFValidateComponent,
            SWITCH(importTRFModelRouterComponent).TO(
                SubChain_PushImport2TRF.tag("PUSH"),
                SubChain_PullImportTRF.tag("PULL")
            ).DEFAULT(Import2TRF.tag("default"))
        );
    </chain>

    <chain name="CanSyncTrfForIterator">
        ITERATOR(syncTrfForActionComponent).DO(THEN(CanSync2TRF_Single));
    </chain>
    <chain name="ForIterator">
        ITERATOR(syncTrfForActionComponent).DO(THEN(Sync2TRF_Single));
    </chain>
    <chain name="Sync2TRF_Single">
        THEN(
            syncTRFDataValidateComponent,
            syncTRFConvertComponent,
            SWITCH(syncTRFRouterComponent).TO(
            SyncCompleted.tag("SyncCompleted"),
            SyncCancel.tag("SyncCancel"),
            SyncClosed.tag("SyncClosed"),
            SyncTesting.tag("SyncTesting"),
            SyncReporting.tag("SyncReporting"),
            SyncPending.tag("SyncPending"),
            SyncToOrder.tag("SyncToOrder"),
            SyncToQuotation.tag("SyncToQuotation"),
            SyncReviseReport.tag("SyncReviseReport"),
            SyncConfirmed.tag("SyncConfirmed"),
            SyncUnBind.tag("SyncUnBind"),
            SyncUnPending.tag("SyncUnPending"),
            SyncUpdateInfo.tag("SyncUpdateInfo"),
            SyncModify.tag("SyncModify")
            ),syncTRFEventNotifyActionComponent
        );
    </chain>
    <chain name="CanSync2TRF_Single">
        THEN(
        syncTRFDataValidateComponent,
        syncTRFConvertComponent,
        SWITCH(syncTRFRouterComponent).TO(
            CanSyncReviseReport.tag("SyncReviseReport")
        )
        );
    </chain>
    <chain name="Sync2TRF">
        THEN(
            syncTRFSpecialComponent,
            syncTRFPreHandleComponent,
            syncTRFCommonValidateComponent,
            SWITCH(syncTRFValidateRouterComponent).
            TO(
                reviseReportSyncActionValidatorComponent.tag("SyncReviseReport"),
                closeSyncActionValidateComponent.tag("SyncClosed"),
                completedSyncActionValidatorComponent.tag("SyncCompleted")).
            DEFAULT(normalSyncActionValidatorComponent),
            ForIterator
        );
    </chain>

    <chain name="CanSync2TRF">
        THEN(
        syncTRFSpecialComponent,
        syncTRFPreHandleComponent,
        syncTRFCommonValidateComponent,
        SWITCH(syncTRFValidateRouterComponent).
        TO(
        reviseReportSyncActionValidatorComponent.tag("SyncReviseReport"),
        closeSyncActionValidateComponent.tag("SyncClosed"),
        completedSyncActionValidatorComponent.tag("SyncCompleted")).
        DEFAULT(normalSyncActionValidatorComponent),
        CanSyncTrfForIterator
        );
    </chain>

    <chain name="SyncCancel">
        THEN(syncCancelActionComponent);
    </chain>
    <chain name="SyncCompleted">
        THEN(syncTRFCompletedValidateComponent,
            IF(syncCompletedModelComponent,SyncCompletedDefault.tag("SyncCompletedDefault"))
            .ELSE(SyncCompletedLightModel.tag("SyncCompletedLightModel"))
        );
    </chain>
    <chain name="SyncCompletedLightModel">
        THEN(syncCompletedLightModelActionComponent);
    </chain>
    <chain name="SyncCompletedDefault">
        THEN(syncCompletedActionComponent);
    </chain>
    <chain name="SyncTesting">
        THEN(syncTestingActionComponent,syncTRFOverwriteTestSampleComponent);
    </chain>
    <chain name="SyncReporting">
        THEN(syncTestingActionComponent,syncTRFOverwriteTestSampleComponent);
    </chain>
    <chain name="SyncClosed">
        THEN(syncClosedActionComponent,syncClosedPostProcessComponent);
    </chain>
    <chain name="SyncModify">
        THEN(syncModifyActionComponent);
    </chain>
    <chain name="SyncPending">
        THEN(syncPendingActionComponent,syncPendingStatusChangeEventComponent,eventNotifyActionComponent);
    </chain>
    <chain name="SyncToOrder">
        THEN(syncToOrderActionComponent,syncTRFOverwriteTestSampleComponent);
    </chain>
    <chain name="SyncToQuotation">
        THEN(syncToQuotationActionComponent);
    </chain>

    <chain name="CanSyncReviseReport">
        THEN(canSyncReviseReportActionComponent);
    </chain>

    <chain name="SyncReviseReport">
        THEN(syncReviseReportActionComponent);
    </chain>

    <chain name="SyncConfirmed">
        THEN(syncConfirmedActionComponent,syncTRFOverwriteTestSampleComponent);
    </chain>
    <chain name="SyncUnBind">
        THEN(syncUnBindActionComponent,syncUnBindEventComponent);
    </chain>
    <chain name="SyncUnPending">
        THEN(syncUnPendingActionComponent,syncPendingStatusChangeEventComponent,eventNotifyActionComponent);
    </chain>
    <chain name="SyncUpdateInfo">
        THEN(syncUpdateInfoActionComponent);
    </chain>
</flow>
