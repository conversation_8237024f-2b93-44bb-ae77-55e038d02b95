<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfOrderExtMapper">

    <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="trf_id" property="trfId" jdbcType="BIGINT" />
        <result column="trf_no" property="trfNo" jdbcType="VARCHAR" />
        <result column="ref_system_id" property="refSystemId" jdbcType="INTEGER" />
        <result column="system_id" property="systemId" jdbcType="INTEGER" />
        <result column="order_id" property="orderId" jdbcType="VARCHAR" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="enquiry_no" property="enquiryNo" jdbcType="VARCHAR" />
        <result column="order_status" property="orderStatus" jdbcType="INTEGER" />
        <result column="order_expect_due_date" property="orderExpectDueDate" jdbcType="TIMESTAMP" />
        <result column="bound_status" property="boundStatus" jdbcType="INTEGER" />
        <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
        <result column="pending_flag" property="pendingFlag" jdbcType="TINYINT" />
        <result column="pending_type" property="pendingType" jdbcType="VARCHAR" />
        <result column="pending_remark" property="pendingRemark" jdbcType="VARCHAR" />
        <result column="cancel_type" property="cancelType" jdbcType="INTEGER" />
        <result column="cancel_remark" property="cancelRemark" jdbcType="VARCHAR" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, trf_id, trf_no, ref_system_id, system_id, order_id, order_no, enquiry_no, order_status,
        order_expect_due_date, bound_status, active_indicator, pending_flag, pending_type,
        pending_remark, cancel_type, cancel_remark, created_by, created_date, modified_by,
        modified_date
    </sql>

    <select id="pageableQueryOrderList" resultType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO">
        <![CDATA[ select id, order_no as orderNo from tb_trf_order where order_status is null and id > #{minId} limit #{offset} ]]>
    </select>
    <select id="selectByTrfNoAndOrderNo" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tb_trf_order
        where order_no = #{orderNo}
        and trf_no = #{trfNo}
        <if test="systemId != null">
            and system_id = #{systemId}
        </if>
        and bound_status = 1
        and active_indicator = 1
        and (order_status != 9 or order_status is null)
    </select>

    <update id="batchUpdateOrder" parameterType="java.util.List">
        <foreach collection="orderStatusList" item="orderStatus" separator=";">
            update tb_trf_order set order_status=#{orderStatus.status} where id=#{orderStatus.id}
        </foreach>
    </update>

</mapper>