package com.sgs.customerbiz.validation.service;

import java.util.*;
import java.util.stream.Collectors;

import com.sgs.customerbiz.validation.validator.Iterator;
import com.sgs.customerbiz.validation.validator.TypeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.sgs.customerbiz.validation.service.parser.ValueParser;
import com.sgs.customerbiz.validation.validator.CheckedType;
import com.sgs.customerbiz.validation.validator.Rule;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ValidationService {

    public static final String VALUE_OF_PARAM = "value";
    public static final String ALIAS_OF_PARAM = "fieldAlias";
    public static final String FIELD_NAME_OF_PARAM = "fieldName";
    public static final String REPORT_NO_OF_PARAM = "reportNo";
    public static final String FIELD_TYPE_OF_PARAM = "fieldType";
    private final Map<String, Rule> ruleMap;
    private final Map<String, ValueParser> valueParserMap;

    public ValidationService(List<Rule> ruleList, List<ValueParser> valueParserList) {
        this.ruleMap = new HashMap<>();
        for (Rule rule : ruleList) {
            ruleMap.put(rule.getClass().getSimpleName(), rule);
            log.info("rule register : {}", rule.getClass().getSimpleName());
        }
        log.info("rule register success total: {}", ruleMap.size());

        this.valueParserMap = new HashMap<>();
        for (ValueParser valueParser : valueParserList) {
            valueParserMap.put(valueParser.key(), valueParser);
            log.info("valueParser register : {}", valueParser.key());
        }
        log.info("valueParser register success total: {}", valueParserMap.size());
    }

    /**
     * 验证数据的有效性。
     *
     * @param fieldValidationConfigs 字段验证配置列表，定义了每个字段如何被验证。
     * @param data                   待验证的数据。
     * @return 返回一个验证结果列表，包含每个字段的验证结果。
     */
    public List<ValidationResult> validate(List<FieldValidationConfig> fieldValidationConfigs, ValidationData data) {
        // 针对每个字段验证配置，先进行类型检查，并收集所有类型检查结果。
        List<ValidationResult> typeCheckedResults = fieldValidationConfigs.stream()
                .map(config -> validateType(data, config))
                .collect(Collectors.toList());

        // 检查是否有类型检查失败的结果。
        boolean hasFail = typeCheckedResults.stream().anyMatch(ValidationResult::isFail);
        if (hasFail) {
            // 如果有类型检查失败，直接返回类型检查结果。
            return typeCheckedResults;
        }

        // 如果所有字段的类型检查都通过，则进行更详细的验证，并返回所有详细的验证结果。
        return fieldValidationConfigs.stream()
                .flatMap(config -> validate(data, config).stream())
                .collect(Collectors.toList());
    }

    /**
     * 根据字段验证配置验证字段类型。
     *
     * @param data                  验证数据对象，包含需要验证的报告信息。
     * @param fieldValidationConfig 字段验证配置对象，定义了字段的验证规则。
     * @return ValidationResult 验证结果对象，包含了验证是否通过的信息。
     * <p>
     * 该方法通过构建参数表，提取参数值，获取字段值，并结合规则配置来验证字段的类型是否符合预期。
     * 它首先初始化一个参数表，用于存储待验证字段的相关信息，如报告号、字段名和字段类型。
     * 然后，通过提取参数值和获取字段实际值，进一步丰富参数表的内容。
     * 接着，根据字段类型配置对应的规则，并构建规则配置对象。
     * 最后，利用规则对象对字段值进行验证，并返回验证结果。
     */
    private ValidationResult validateType(ValidationData data, FieldValidationConfig fieldValidationConfig) {
        // 初始化参数表，用于存储待验证字段的相关信息
        Map<String, Object> paramTable = new HashMap<>();
        paramTable.put(REPORT_NO_OF_PARAM, "$.Report[0].reportNo");
        paramTable.put(ALIAS_OF_PARAM, fieldValidationConfig.getFieldAlias());
        paramTable.put(FIELD_NAME_OF_PARAM, fieldValidationConfig.getFieldName());
        paramTable.put(FIELD_TYPE_OF_PARAM, fieldValidationConfig.getFieldType());

        // 提取参数值，根据参数表和验证数据对象获取相关值
        Map<String, Object> paramValueMap = ValidationUtils.extractParamValue(paramTable, data, valueParserMap);

        // 获取字段的实际值
        Object value = ValidationUtils.getValue(data, fieldValidationConfig, valueParserMap);
        // 将字段值添加到参数值映射表中
        paramValueMap.put(VALUE_OF_PARAM, value);

        // 根据字段类型获取对应的规则对象
        Rule rule = ruleMap.get(CheckedType.class.getSimpleName());

        // 构建字段规则配置对象，用于配置验证规则的详细信息
        FieldRuleConfig typeRuleConfig = new FieldRuleConfig();
        typeRuleConfig.setRuleCode(CheckedType.class.getSimpleName());
        typeRuleConfig.setParamTable(JSON.toJSONString(paramValueMap));
        typeRuleConfig.setRuleConfig(RuleConfig.findRuleConfig(CheckedType.class.getSimpleName()));

        // 使用规则对象验证字段值，并返回验证结果
        return rule.validate(value, paramValueMap, fieldValidationConfig, typeRuleConfig);
    }


    private List<ValidationResult> validate(ValidationData data, FieldValidationConfig fieldValidationConfig) {
        return fieldValidationConfig.getFieldRuleConfigList().stream()
                .map(frc -> validate(data, fieldValidationConfig, frc))
                .collect(Collectors.toList());
    }

    private ValidationResult validate(ValidationData data,
                                      FieldValidationConfig fieldValidationConfig,
                                      FieldRuleConfig fieldRuleConfig) {
        Map<String, Object> paramValueMap = ValidationUtils.extractParamValue(fieldRuleConfig.paramTable(), data, valueParserMap);
        Object value = ValidationUtils.getValue(data, fieldValidationConfig, valueParserMap);
        paramValueMap.put(VALUE_OF_PARAM, value);
        paramValueMap.put(ALIAS_OF_PARAM, fieldValidationConfig.getFieldAlias());
        paramValueMap.put(FIELD_NAME_OF_PARAM, fieldValidationConfig.getFieldName());

        if(fieldRuleConfig.getRuleCode().equalsIgnoreCase("Iterator")) {
            if(Objects.isNull(value)) {
                return ValidationResult.ok(fieldValidationConfig, fieldRuleConfig, value, paramValueMap);
            }
            if(TypeUtils.isNumeric()){
                return ValidationResult.ok(fieldValidationConfig, fieldRuleConfig, value, paramValueMap);
            }
            if(value instanceof Collection && isEmptyCollection((Collection<?>) value)) {
                return ValidationResult.ok(fieldValidationConfig, fieldRuleConfig, value, paramValueMap);
            }
        }
        Rule rule = ruleMap.get(fieldRuleConfig.getRuleCode());
        return rule.validate(value, paramValueMap, fieldValidationConfig, fieldRuleConfig);
    }

}
