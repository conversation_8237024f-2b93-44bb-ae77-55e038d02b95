package com.sgs.customerbiz.validation.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableList;
import com.sgs.customerbiz.core.util.OptionalUtils;
import com.sgs.customerbiz.validation.dbstorages.mybatis.mapper.SGSCustomerFieldMapper;
import com.sgs.customerbiz.validation.dbstorages.mybatis.mapper.SGSFieldValidationRuleMapper;
import com.sgs.customerbiz.validation.dbstorages.mybatis.mapper.SGSModelFieldMapper;
import com.sgs.customerbiz.validation.dbstorages.mybatis.mapper.SGSValidationRuleMapper;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSCustomerFieldExample;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSCustomerFieldPO;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSFieldValidationRuleExample;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSFieldValidationRulePO;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSModelFieldExample;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSModelFieldPO;
import com.sgs.customerbiz.validation.dbstorages.mybatis.model.SGSValidationRuleExample;
import com.sgs.customerbiz.validation.newtypes.CustomerGroupNo;
import com.sgs.customerbiz.validation.newtypes.CustomerNo;
import com.sgs.customerbiz.validation.newtypes.RefSystemId;
import com.sgs.customerbiz.validation.props.ValidationProps;
import com.sgs.customerbiz.validation.service.join.Joiner;
import com.sgs.customerbiz.validation.utils.DffUtils;
import com.sgs.framework.model.enums.ProductLineType;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ValidationConfigService {

    private final SGSModelFieldMapper fieldMapper;
    private final SGSCustomerFieldMapper customerFieldMapper;
    private final SGSFieldValidationRuleMapper fieldValidationRuleMapper;
    private final SGSValidationRuleMapper validationRuleMapper;
    private final Joiner joiner;

    public ValidationConfigService(SGSModelFieldMapper fieldMapper,
                                   SGSCustomerFieldMapper customerFieldMapper,
                                   SGSFieldValidationRuleMapper fieldValidationRuleMapper,
                                   SGSValidationRuleMapper validationRuleMapper,
                                   Joiner joiner) {
        this.fieldMapper = fieldMapper;
        this.customerFieldMapper = customerFieldMapper;
        this.fieldValidationRuleMapper = fieldValidationRuleMapper;
        this.validationRuleMapper = validationRuleMapper;
        this.joiner = joiner;
    }

    public Optional<List<FieldValidationConfig>> findBasic() {
        List<SGSModelFieldPO> sgsModelFieldPOS = fieldMapper.selectByExample(new SGSModelFieldExample());
        Optional<List<FieldValidationConfig>> fieldValidationConfigs = readBasicConfigBy(sgsModelFieldPOS);
        return fieldValidationConfigs;
    }

    public Optional<List<FieldValidationConfig>> findBy(String productLineCode, CustomerGroupNo groupNo) {
        SGSCustomerFieldExample example = new SGSCustomerFieldExample();
        SGSCustomerFieldExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerGroupCodeEqualTo(groupNo.val());
        OptionalUtils.whenNotBlank(productLineCode).ifPresent(buCode -> criteria.andProductLineCodeIn(ImmutableList.of(buCode, ProductLineType.ALL.getProductLineAbbr())));
        List<SGSCustomerFieldPO> customerFieldList = findCustomerFieldByExample(example);
        Optional<List<FieldValidationConfig>> fieldValidationConfigs = readConfigBy(customerFieldList);
        return fieldValidationConfigs;
    }

    public Optional<List<FieldValidationConfig>> findBy(String productLineCode, CustomerNo customerNo) {
        SGSCustomerFieldExample example = new SGSCustomerFieldExample();
        SGSCustomerFieldExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerNoEqualTo(customerNo.val());
        OptionalUtils.whenNotBlank(productLineCode).ifPresent(buCode -> criteria.andProductLineCodeIn(ImmutableList.of(buCode, ProductLineType.ALL.getProductLineAbbr())));
        List<SGSCustomerFieldPO> customerFieldList = findCustomerFieldByExample(example);
        Optional<List<FieldValidationConfig>> fieldValidationConfigs = readConfigBy(customerFieldList);
        return fieldValidationConfigs;
    }

    public Optional<List<FieldValidationConfig>> findBy(String productLineCode, RefSystemId refSystemId) {
        SGSCustomerFieldExample example = new SGSCustomerFieldExample();
        SGSCustomerFieldExample.Criteria criteria = example.createCriteria();
        criteria.andRefSystemIdEqualTo(refSystemId.val());
        OptionalUtils.whenNotBlank(productLineCode).ifPresent(buCode -> criteria.andProductLineCodeIn(ImmutableList.of(buCode, ProductLineType.ALL.getProductLineAbbr())));
        List<SGSCustomerFieldPO> customerFieldList = findCustomerFieldByExample(example);
        Optional<List<FieldValidationConfig>> fieldValidationConfigs = readConfigBy(customerFieldList);
        return fieldValidationConfigs;
    }

    public JoinerBuilder prepareJoin() {
        return new JoinerBuilder(joiner);
    }

    public static class JoinerBuilder {
        private Optional<List<FieldValidationConfig>> basic = Optional.empty();
        private Optional<List<FieldValidationConfig>> customer = Optional.empty();
        private final Joiner joiner;

        public JoinerBuilder(Joiner joiner) {
            this.joiner = joiner;
        }

        public Optional<List<FieldValidationConfig>> join() {
            return joiner.join(basic.orElse(Collections.emptyList()), customer.orElse(Collections.emptyList()));
        }

        public JoinerBuilder basic(Optional<List<FieldValidationConfig>> basic) {
            this.basic = basic;
            return this;
        }

        public JoinerBuilder customer(Optional<List<FieldValidationConfig>> customer) {
            this.customer = customer;
            return this;
        }
    }

    public static void replaceDffCode(Map<String, String> dffCodeMapping, List<FieldValidationConfig> validationConfigs, ValidationProps props) {
        for (FieldValidationConfig fieldValidationConfig : validationConfigs) {
            if(fieldValidationConfig.notCustomized() || props.notReplace(fieldValidationConfig.getRefSystemId())) {
                continue;
            }
            String afterProcessExpr = DffUtils.replaceDffLabelCode(fieldValidationConfig.getExpr(), dffCodeMapping);
            fieldValidationConfig.setExpr(afterProcessExpr);
            for (FieldRuleConfig fieldRuleConfig : fieldValidationConfig.getFieldRuleConfigList()) {
                String paramTableStr = fieldRuleConfig.getParamTable();
                if(paramTableStr == null) {
                    continue;
                }
                Map<String, Object> paramTable = JSON.parseObject(paramTableStr, new TypeReference<Map<String, Object>>() {
                });
                Map<String, Object> newParamTable = new HashMap<>();
                for (Map.Entry<String, Object> kvPair : paramTable.entrySet()) {
                    String key = kvPair.getKey();
                    Object value = kvPair.getValue();
                    if(value instanceof String) {
                        String strVal = (String)value;
                        if(strVal.startsWith("$")) {
                            newParamTable.put(key, DffUtils.replaceDffLabelCode(strVal, dffCodeMapping));
                        } else {
                            newParamTable.put(key, value);
                        }
                    } else {
                        newParamTable.put(key, value);
                    }
                }
                fieldRuleConfig.setParamTable(JSON.toJSONString(newParamTable));
            }
        }
    }

    public Optional<List<FieldValidationConfig>> readBasicConfigBy(List<SGSModelFieldPO> modelFieldPOList) {
        if(CollectionUtils.isEmpty(modelFieldPOList)) {
            return Optional.empty();
        }

        Map<String, RuleConfig> rules = findAllRuleConfig();

        Map<Integer, List<SGSFieldValidationRulePO>> fieldRuleMap = findModelFieldValidationRuleBy(modelFieldPOList);

        List<FieldValidationConfig> fieldValidationConfigs = modelFieldPOList.stream()
                .map(modelFieldPO -> createFieldValidationConfigFrom(modelFieldPO, fieldRuleMap, rules))
                .collect(Collectors.toList());

        // Process fieldValidationConfigs to set children for elements with 'Requered' rule
        // Create a map of parentId to FieldValidationConfig for quick parent lookup
        Map<Integer, List<FieldValidationConfig>> parentConfigMap = fieldValidationConfigs.stream()
                .filter(config -> config.getParentId() != null && config.getParentId() > 0)
                .collect(Collectors.groupingBy(FieldValidationConfig::getParentId));

        // Create a list of ids to remove to avoid concurrent modification
        List<Integer> idsToRemove = new ArrayList<>();

        fieldValidationConfigs.forEach(config -> {
            boolean hasRequiredRule = Optional.ofNullable(config.getFieldRuleConfigList()).orElse(Collections.emptyList())
                    .stream().anyMatch(rule -> "Requered".equals(rule.getRuleCode()));
            if (hasRequiredRule) {
                List<FieldValidationConfig> children = parentConfigMap.get(config.getId());
                if (children != null && !children.isEmpty()) {
                    config.setChildren(children);
                    idsToRemove.addAll(children.stream().map(FieldValidationConfig::getId).collect(Collectors.toList()));
                }
            }
        });

        // Remove configs that were added as children
        fieldValidationConfigs.removeIf(config -> idsToRemove.contains(config.getId()));

        if(CollectionUtils.isEmpty(fieldValidationConfigs)) {
            return Optional.empty();
        }
        return Optional.of(fieldValidationConfigs);
    }

    public Optional<List<FieldValidationConfig>> readConfigBy(List<SGSCustomerFieldPO> customerFieldPOList) {
        if(CollectionUtils.isEmpty(customerFieldPOList)) {
            return Optional.empty();
        }

        Map<String, RuleConfig> rules = findAllRuleConfig();

        Map<Integer, List<SGSFieldValidationRulePO>> fieldRuleMap = findFieldValidationRuleBy(customerFieldPOList);

        List<FieldValidationConfig> fieldValidationConfigs = customerFieldPOList.stream()
                .map(sgsCustomerFieldPO -> createFieldValidationConfigFrom(sgsCustomerFieldPO, fieldRuleMap, rules))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fieldValidationConfigs)) {
            return Optional.empty();
        }
        return Optional.of(fieldValidationConfigs);
    }

    private Map<Integer, List<SGSFieldValidationRulePO>> findFieldValidationRuleBy(List<SGSCustomerFieldPO> customerFieldPOList) {
        List<Integer> customerFieldIdList = customerFieldPOList.stream()
                .map(SGSCustomerFieldPO::getId)
                .collect(Collectors.toList());
        return findFieldValidationRuleBy(1, customerFieldIdList);
    }

    private Map<Integer, List<SGSFieldValidationRulePO>> findModelFieldValidationRuleBy(List<SGSModelFieldPO> modelFieldPOList) {
        List<Integer> customerFieldIdList = modelFieldPOList.stream()
                .map(SGSModelFieldPO::getId)
                .collect(Collectors.toList());
        return findFieldValidationRuleBy(0, customerFieldIdList);
    }

    private Map<Integer, List<SGSFieldValidationRulePO>> findFieldValidationRuleBy(int customer, List<Integer> customerFieldIdList) {
        SGSFieldValidationRuleExample fieldRuleExample = new SGSFieldValidationRuleExample();
        fieldRuleExample.createCriteria().andCustomizedEqualTo(customer).andFieldIdIn(customerFieldIdList);
        return fieldValidationRuleMapper.selectByExample(fieldRuleExample).stream()
                .collect(Collectors.groupingBy(SGSFieldValidationRulePO::getFieldId));
    }


    private Map<String, RuleConfig> findAllRuleConfig() {
        return validationRuleMapper.selectByExample(new SGSValidationRuleExample()).stream()
                .map(rulePo -> RuleConfig.of(rulePo.getRuleCode(), rulePo.getMessageTemplate()))
                .collect(Collectors.toMap(RuleConfig::getRuleCode, Function.identity()));
    }

    private static FieldValidationConfig createFieldValidationConfigFrom(SGSCustomerFieldPO sgsCustomerFieldPO, Map<Integer, List<SGSFieldValidationRulePO>> fieldRuleMap, Map<String, RuleConfig> rules) {
        FieldValidationConfig fieldValidationConfig = FieldValidationConfig.createFrom(sgsCustomerFieldPO);
        List<FieldRuleConfig> fieldRuleConfigList = fieldRuleMap.getOrDefault(sgsCustomerFieldPO.getId(), Collections.emptyList()).stream()
                .map(fieldRulePO -> createFieldRuleConfigFrom(fieldRulePO, rules))
                .collect(Collectors.toList());
        fieldValidationConfig.setFieldRuleConfigList(fieldRuleConfigList);
        return fieldValidationConfig;
    }

    private static FieldValidationConfig createFieldValidationConfigFrom(SGSModelFieldPO modelField, Map<Integer, List<SGSFieldValidationRulePO>> fieldRuleMap, Map<String, RuleConfig> rules) {
        FieldValidationConfig fieldValidationConfig = FieldValidationConfig.createFrom(modelField);
        List<FieldRuleConfig> fieldRuleConfigList = fieldRuleMap.getOrDefault(modelField.getId(), Collections.emptyList()).stream()
                .map(fieldRulePO -> createFieldRuleConfigFrom(fieldRulePO, rules))
                .collect(Collectors.toList());
        fieldValidationConfig.setFieldRuleConfigList(fieldRuleConfigList);
        return fieldValidationConfig;
    }

    private static FieldRuleConfig createFieldRuleConfigFrom(SGSFieldValidationRulePO fieldRulePO, Map<String, RuleConfig> rules) {
        String ruleCode = fieldRulePO.getRuleCode();
        RuleConfig ruleConfig = rules.computeIfAbsent(ruleCode, code -> 
            RuleConfig.findByRuleCode(code)
                .orElseThrow(() -> new IllegalStateException("ruleConfig is null by ruleCode = " + code)));
        return FieldRuleConfig.createFrom(fieldRulePO, ruleConfig);
    }


    public List<SGSCustomerFieldPO> findCustomerFieldByExample(SGSCustomerFieldExample exampleObj) {
        return customerFieldMapper.selectByExample(exampleObj);
    }
}
