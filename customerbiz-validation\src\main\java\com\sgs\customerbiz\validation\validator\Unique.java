package com.sgs.customerbiz.validation.validator;

import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class Unique extends AbstractOkIfNullValueRule {

    @Override
    protected ValidationResult doValidateIfValueNotNull(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig) {
        ValidationResult.ValidationResultContext ctx = ValidationResult.ctx(fieldValidationConfig, ruleConfig, value, params);
        if (!(value instanceof Collection)) {
            ctx.ok();
        }
        Collection<?> coll = ((Collection<?>) value).stream().filter(Objects::nonNull).collect(Collectors.toList());
        Set<?> set = new HashSet<>(coll);
        boolean hasDuplicates = coll.size() != set.size();

        if (hasDuplicates) {
            return ctx.fail();
        } else {
            return ctx.ok();
        }
    }
}
