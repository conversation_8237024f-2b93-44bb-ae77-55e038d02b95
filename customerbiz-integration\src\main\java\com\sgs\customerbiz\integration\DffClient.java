package com.sgs.customerbiz.integration;

import com.alibaba.fastjson.*;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.customerbiz.core.config.InterfaceConfig;
import com.sgs.customerbiz.core.util.HttpClientUtil;
import com.sgs.customerbiz.integration.dto.DffFormGeneralRsp;
import com.sgs.customerbiz.integration.dto.DffMappingConfigReq;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.dto.dff.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DffClient {
    private static final Logger logger = LoggerFactory.getLogger(DffClient.class);
    @Resource
    private InterfaceConfig interfaceConfig;

    /**
     * @param formIdSets
     * @return
     */
    public List<DffFormAttrDTO> getDffFormAttrByDffFormIdList(Set<String> formIdSets) {
        long startMillis = System.currentTimeMillis();
        try {
            String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormAttrByDffFormIDList", interfaceConfig.getBaseUrl());
            Map<String, Object> maps = Maps.newHashMap();
            maps.put("dffIDList", formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOS = HttpClientUtil.post(dffApiUrl, maps, DffFormAttrDTO.class);
            List<DffFormAttrDTO> dffFormAttrDTOList = Lists.newArrayList();
            dffFormAttrDTOS.forEach(dffFormAttrDTO -> {
                dffFormAttrDTO.setLanguageID(LanguageType.findCode(dffFormAttrDTO.getLanguageCode()).getLanguageId());
                dffFormAttrDTOList.add(dffFormAttrDTO);
                List<DffFormAttrDisplayDTO> dffFormAttrDisplayDTOS = dffFormAttrDTO.getDisplayNameMultLanguage();
                if (CollectionUtils.isNotEmpty(dffFormAttrDisplayDTOS)) {
                    dffFormAttrDisplayDTOS = dffFormAttrDisplayDTOS.stream().filter(dffFormAttrDisplayDTO -> {
                        return !StringUtils.equalsIgnoreCase(dffFormAttrDisplayDTO.getLanguageCode(), LanguageType.English.getCode());
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(dffFormAttrDisplayDTOS)) {
                        for (DffFormAttrDisplayDTO dffFormAttrDisplayDTO : dffFormAttrDisplayDTOS) {
                            DffFormAttrDTO mutiLanguageDffFormAttrDisplayDTO = new DffFormAttrDTO();
                            BeanUtils.copyProperties(dffFormAttrDTO, mutiLanguageDffFormAttrDisplayDTO);
                            mutiLanguageDffFormAttrDisplayDTO.setdFFAttrID(dffFormAttrDisplayDTO.getDffFormAttrId());
                            mutiLanguageDffFormAttrDisplayDTO.setDispalyName(dffFormAttrDisplayDTO.getDisplayName());
                            mutiLanguageDffFormAttrDisplayDTO.setLanguageCode(dffFormAttrDisplayDTO.getLanguageCode());
                            mutiLanguageDffFormAttrDisplayDTO.setLanguageID(LanguageType.findCode(dffFormAttrDisplayDTO.getLanguageCode()).getLanguageId());
                            dffFormAttrDTOList.add(mutiLanguageDffFormAttrDisplayDTO);

                        }
                    }
                }
            });
            return dffFormAttrDTOList;
        } catch (Exception e) {
            logger.error("DffClient.getDffFormAttrByDffFormIdList Error : {}", e.getMessage(), e);
        } finally {
            logger.info("DffClient.getDffFormAttrByDffFormIdList 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }

    public Optional<List<DffMappingConfigRsp>> queryCustomerDffMapping(String buCode, String customerGroupCode, String formId, String gridId) {
        if(StringUtils.isBlank(buCode) || StringUtils.isBlank(customerGroupCode) || (StringUtils.isBlank(formId) && StringUtils.isBlank(gridId))) {
            logger.debug("buCode: {} || customerGroupCode: {} || (formId: {} && gridId: {}) return empty", buCode, customerGroupCode, formId, gridId);
            return Optional.empty();
        }

        List<JSONArray> groupRespList = new ArrayList<>();
        if(StringUtils.isNotBlank(formId)){
            groupRespList.add(queryDffFormGeneral(formId));
        }
        if(StringUtils.isNotBlank(gridId)){
            groupRespList.add(queryDffFormGeneral(gridId));
        }

        List<String> groupIdList = DffClient.formGroupIdList(groupRespList);
        if(org.springframework.util.CollectionUtils.isEmpty(groupIdList)) {
            return Optional.empty();
        }

        DffMappingConfigReq configReq = new DffMappingConfigReq();
        configReq.setBuCode(buCode);
        configReq.setCustomerGroupCode(customerGroupCode);
        configReq.setFormGroupId(groupIdList);
        return Optional.ofNullable(queryCustomerDffMapping(configReq));
    }

    public Optional<List<DffMappingConfigRsp>> queryCustomerDffMapping0(String buCode, String customerGroupCode, String formId, String gridId) {
        if(StringUtils.isBlank(buCode) || StringUtils.isBlank(customerGroupCode) || (StringUtils.isBlank(formId) && StringUtils.isBlank(gridId))) {
            logger.debug("buCode: {} || customerGroupCode: {} || (formId: {} && gridId: {}) return empty", buCode, customerGroupCode, formId, gridId);
            return Optional.empty();
        }

        DffMappingConfigReq configReq = new DffMappingConfigReq();
        configReq.setBuCode(buCode);
        configReq.setCustomerGroupCode(customerGroupCode);
        configReq.setFormGroupId(ImmutableList.of(formId, gridId));
        return Optional.ofNullable(queryCustomerDffMapping(configReq));
    }


    public List<DffMappingConfigRsp> queryCustomerDffMapping(DffMappingConfigReq req) {
        long startMillis = System.currentTimeMillis();
        String url = String.format("%s/DFFV2Api/dff/queryCustomerDffMapping", interfaceConfig.getBaseUrl());
        try {
            BaseResponse response = HttpClientUtil.doPost(url, req, BaseResponse.class);
            if (response.getStatus() != 200) {
                logger.error("DffClient.queryCustomerDffMapping 异常，异常信息{}", response.getMessage());
                return null;
            }
            return JSONArray.parseArray(JSONObject.toJSONString(response.getData()), DffMappingConfigRsp.class);
        } catch (Exception e) {
            logger.info("DffClient.queryCustomerDffMapping Error : {}", e.getMessage());
        } finally {
            logger.info("DffClient.queryCustomerDffMapping 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }

    public static List<String> formGroupIdList(List<JSONArray> groupRespList) {
        return groupRespList.stream()
                .filter(groupResp -> CollectionUtils.isNotEmpty(groupResp)
                        && groupResp.getJSONObject(0).containsKey("formGroupID")
                        && StringUtils.isNotBlank(groupResp.getJSONObject(0).getString("formGroupID")))
                .map(groupResp -> groupResp.getJSONObject(0).getString("formGroupID"))
                .collect(Collectors.toList());
    }

    public static Optional<String> firstFormGroupId(JSONArray groupResp) {
        if(CollectionUtils.isEmpty(groupResp)
                || ! groupResp.getJSONObject(0).containsKey("formGroupID")
                || StringUtils.isBlank(groupResp.getJSONObject(0).getString("formGroupID"))) {
            return Optional.empty();
        }
        return Optional.of(groupResp.getJSONObject(0).getString("formGroupID"));
    }


    public String queryTemplateName(String templateId) {
        JSONArray objects = queryDffFormGeneral(templateId);
        if (Func.isEmpty(objects)) {
            return null;
        }
        return objects.getJSONObject(0).getString("name");
    }


    public JSONArray queryDffFormGeneral(String templateId) {
        long startMillis = System.currentTimeMillis();
        String url = String.format("%s/DFFV2Api/dff/queryDffFormGeneral", interfaceConfig.getBaseUrl());
        try {
            String response = HttpClientUtil.doPostFrom(url, ImmutableMap.of("id", templateId));
            return JSONArray.parseArray(response);
        } catch (Exception e) {
            logger.info("DffClient.queryDffFormGeneral Error : {}", e.getMessage());
        } finally {
            logger.info("DffClient.queryDffFormGeneral 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }

    private List<DffFormGeneralRsp> queryDffFormGeneralByName(String buCode, String customerGroupCode, String templateName) {
        long startMillis = System.currentTimeMillis();
        String url = String.format("%s/DFFV2Api/dff/queryDffFormGeneral", interfaceConfig.getBaseUrl());
        try {
            String response = HttpClientUtil.post(url, ImmutableMap.of("name", templateName, "buCode",buCode, "customerGroupCode", customerGroupCode));
            return JSONArray.parseArray(response, DffFormGeneralRsp.class);

        } catch (Exception e) {
            logger.info("DffClient.queryDffFormGeneral Error : {}", e.getMessage());
        } finally {
            logger.info("DffClient.queryDffFormGeneral 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return null;
    }

    public Map<String, String> queryDffTemplateIdByName(String buCode, String customerGroupCode,String templateName) {
        Map<String, String> map = new HashMap<>();
        List<DffFormGeneralRsp> resultArray = queryDffFormGeneralByName(buCode, customerGroupCode,templateName);
        if (Func.isEmpty(resultArray)) {
            return map;
        }
        String formId = resultArray.stream().filter(dffFormGeneralRsp -> Objects.equals(dffFormGeneralRsp.getType(), "FORM")).map(DffFormGeneralRsp::getId).findFirst().orElse("");
        String gridId = resultArray.stream().filter(dffFormGeneralRsp -> Objects.equals(dffFormGeneralRsp.getType(), "GRID")).map(DffFormGeneralRsp::getId).findFirst().orElse("");
        map.put("formId", formId);
        map.put("gridId", gridId);
        return map;
    }

    public String getDffNameByID(String dffFormID) {
        String dffName = "";
        String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormNoPage", interfaceConfig.getBaseUrl());
        Map<String, Object> maps = Maps.newHashMap();
        maps.put("id", dffFormID);
        try {
            String jsonStr = HttpClientUtil.post(dffApiUrl, maps);
            logger.info("getDffNameByID call {}  result is {}", dffApiUrl, jsonStr);
            List<DffFormDTO> dffFormAttrDTOS = JSON.parseArray(jsonStr, DffFormDTO.class);
            if (CollectionUtils.isNotEmpty(dffFormAttrDTOS)) {
                dffName = dffFormAttrDTOS.get(0).getName();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return dffName;
        }
        return dffName;

    }

    public List<DffFormComfirmDTO> getDffFormComfirmIdBatchByIds(Set<String> dffFormIds) {
        long startMillis = System.currentTimeMillis();
        List<DffFormComfirmDTO> dffFormComfirmDTOs = Lists.newArrayList();
        try {
            /**
             * 根据formID获取dff attr信息
             */
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("dffIDList", dffFormIds);
            String dffApiUrl = String.format("%s/DFFV2Api/dff/queryDffFormComfirmIdBatchByIds", interfaceConfig.getBaseUrl());
            String strResult = HttpClientUtil.postJson(dffApiUrl, map);
            JSONObject jsonObject = JSONObject.parseObject(strResult);
            dffFormComfirmDTOs = JSONObject.parseArray(jsonObject.getString("rows"), DffFormComfirmDTO.class);

        } catch (Exception e) {
            logger.error("DffClient.getDffFormComfirmIdBatchByIds Error : {}", e.getMessage(), e);
        } finally {
            logger.info("DffClient.getDffFormComfirmIdBatchByIds 请求接口耗时：{}.", (System.currentTimeMillis() - startMillis));
        }
        return dffFormComfirmDTOs;
    }

    public List<DFFFormRspDTO> queryDFF(String dffFormId, Integer languageId) {
        List<DFFFormRspDTO> dffFormRspDTOS = Lists.newArrayList();
        try {
            Map<String, Object> params = Maps.newHashMap();
            params.put("id", dffFormId);
            params.put("languageID", languageId == null ? 1 : languageId);
            LanguageType code = LanguageType.findLanguageId(languageId);
            params.put("languageCode", code != null ? code.getCode() : null);
            String queryURL = String.format("%s/DFFV2Api/dff/queryDff", interfaceConfig.getBaseUrl());
            dffFormRspDTOS = HttpClientUtil.post(queryURL, params, new TypeReference<List<DFFFormRspDTO>>() {
            });
        } catch (Exception e) {
            logger.error("query DFF ,DFFFormID:{} error", dffFormId, e);
        }
        return dffFormRspDTOS;

    }


}
