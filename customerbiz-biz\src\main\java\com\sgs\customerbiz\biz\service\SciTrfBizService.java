package com.sgs.customerbiz.biz.service;

import java.io.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.googlecode.aviator.script.AviatorBindings;
import com.sgs.config.api.constant.ConfigKeyConstant;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.customerbiz.biz.config.TrfBizConfig;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.biz.dff.DFFConvertor;
import com.sgs.customerbiz.biz.enums.CustomerReportDeliveryMode;
import com.sgs.customerbiz.biz.event.EventUtils;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.customerbiz.biz.helper.DfvHelper;
import com.sgs.customerbiz.biz.service.delivery.DeliveryActionExtPt;
import com.sgs.customerbiz.biz.service.importtrf.ImportTrfActionExtPt;
import com.sgs.customerbiz.biz.service.importtrf.v2.CustomerTrfInvoker;
import com.sgs.customerbiz.biz.service.importtrf.v2.OrderToTrfInvoker;
import com.sgs.customerbiz.biz.service.order2trf.OrderToTrfActionExtPt;
import com.sgs.customerbiz.biz.service.ruleengine.node.CheckReportExist;
import com.sgs.customerbiz.biz.service.synctrf.SyncActionExtPt;
import com.sgs.customerbiz.biz.service.synctrf.SyncActionValidatorExtPt;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.todolist.CustomerOrderService;
import com.sgs.customerbiz.biz.service.todolist.CustomerTrfToSgsTrfService;
import com.sgs.customerbiz.biz.utils.ScheduledWhiteList;
import com.sgs.customerbiz.biz.utils.TempSendEmailExecutor;
import com.sgs.customerbiz.core.common.SciRequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.exception.ErrorAssert;
import com.sgs.customerbiz.core.util.ConditionUtils;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfTodoInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.CustomerTrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.domainevent.*;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.domain.domainservice.TrfApproveDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.*;
import com.sgs.customerbiz.integration.dto.*;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.dto.resp.*;
import com.sgs.customerbiz.model.trf.enums.*;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.facade.model.req.rd.BatchExistReportDataReq;
import com.sgs.testdatabiz.facade.model.req.rd.BatchExportReportDataReq;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportDataReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.script.Bindings;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import static com.sgs.customerbiz.biz.utils.TrfEventCodeMapping.TRF_ACTION_REVIEW_CONCLUSION;
import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class SciTrfBizService {
    @Resource
    private ExtensionExecutor extensionExecutor;

    @Resource
    private TrfDomainService trfDomainService;

    @Resource
    private TrfBizConfig trfBizConfig;

    @Resource
    private EventSubscribeService eventSubscribeService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private DfvHelper dfvHelper;

    @Autowired
    private TrfInfoExtMapper trfInfoExtMapper;

    @Resource
    private TrfTodoInfoExtMapper trfTodoInfoExtMapper;

    @Autowired
    private List<CustomerTrfInvoker> customerTrfInvokerList;
    private Map<String, CustomerTrfInvoker> customerTrfInvokerMap;
    @Autowired
    private List<OrderToTrfInvoker> orderToTrfInvokerList;
    private Map<String, OrderToTrfInvoker> orderToTrfInvokerMap;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ReportDataClient reportDataClient;

    @Autowired
    private ReportDataService reportDataService;

    @Autowired
    private ConfigClient configClient;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private DFFConvertor dffConvertor;

    @Autowired
    private TrfApproveDomainService trfApproveDomainService;

    @Autowired
    private ScheduledWhiteList scheduledWhiteList;

    @Autowired
    private DffClient dffClient;

    @Autowired
    private CustomerClient customerClient;

    @Autowired
    private TrfReportDomainService trfReportDomainService;

    @Autowired
    private EmailClient emailClient;

    @Autowired
    private FileClient fileClient;

    @Autowired
    private CustomerTrfToSgsTrfService customerTrfToSgsTrfService;

    private static final String VALIDATE_IMPORT_TRF_TPL_GENERAL = "sci-import";
    private static final String VALIDATE_IMPORT_TRF_TPL_CUSTOMER_FORMATTER = "sci-import-{}-{}";
    private static final String DFV_CODE_FUNC_LEVEL_SYNC_TRF = "sci-syncTrf";

    @Autowired
    private ValidationBizService validationBizService;

    @Autowired
    private InspectionClient inspectionClient;

    private final TrfOrderDomainService trfOrderDomainService;


    /**
     * 导入Customer TRF
     *
     * @param importReq importTRF请求
     * @return
     */
    public TrfImportResult importTrf(TrfImportReq importReq) {
        log.info("sci importTrf ,request:[{}]", JSON.toJSONString(importReq));
        Integer refSystemId = validateImportTrf(importReq);

        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, importReq.getImportMode(), getScenarioValue(refSystemId));

        return extensionExecutor.execute(
                ImportTrfActionExtPt.class,
                bizScenario,
                importActionExtPt -> transactionTemplate.execute(transactionStatus -> {
                    TrfImportResult trfImportResult = importActionExtPt.importTrf(importReq);
                    return trfImportResult;
                }));
    }

    public Integer validateImportTrf(TrfImportReq importReq) {
//        // 1.基本参数验证

        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        // 1.基本参数验证
        ErrorAssert.notNull(importReq, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "The request is required");
        ErrorAssert.notNull(importReq.getRefSystemId(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "The request.refSystemId is required!");

        if (StringUtil.isBlank(importReq.getImportMode())) {
            importReq.setImportMode(TrfImportMode.TRF_IMPORT_MODE_PULL);
        }

        Integer refSystemId = importReq.getRefSystemId();
        // 2.请求参数校验(general + by customer)
        dfvHelper.validateParams(importReq, new String[]{VALIDATE_IMPORT_TRF_TPL_GENERAL, StrUtil.format(VALIDATE_IMPORT_TRF_TPL_CUSTOMER_FORMATTER, refSystemId, importReq.getSystemId())});

        // 3.TRF状态校验
//        this.trfStatusCheck(refSystemId, importReq.getTrfNo());
        return refSystemId;
    }

    public TrfImportResult bindTrf(TrfBindReq bindTrfReq) {

        TrfInfoPO trfInfoPO = getTrfInfoPO(bindTrfReq);
        GetCustomerConfigReq customerConfigReq = new GetCustomerConfigReq();
        customerConfigReq.setRefSystemId(bindTrfReq.getRefSystemId());
        ConfigInfo customerConfig = configClient.getCustomerConfig(customerConfigReq);
        Set<String> supportImportModes = Optional.ofNullable(customerConfig)
                .map(ConfigInfo::getConfigValue)
                .filter(StringUtils::isNotBlank)
                .map(configVal -> JSON.parseObject(configVal, CustomerGeneralConfig.class))
                .map(CustomerGeneralConfig::getSupportImportModes)
                .filter(CollectionUtils::isNotEmpty)
                .orElse(CustomerGeneralConfig.DEFAULT_SUPPORT_IMPORT_MODES);

        if(Objects.isNull(trfInfoPO) && !supportImportModes.contains(TrfImportMode.TRF_IMPORT_MODE_PULL)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.SCITRFBIZSERVICE,
                    ErrorFunctionTypeEnum.BIND,
                    ErrorTypeEnum.DATANOTFOUND);
            throw new CustomerBizException(errorCode, "can't bind trf! the refSystem not support pull mode and trf not found by request " + bindTrfReq);
        }

        if(Objects.nonNull(trfInfoPO) && Objects.equals(trfInfoPO.getSource(), TrfSourceType.Order2TRF.getSourceType())) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.SCITRFBIZSERVICE,
                    ErrorFunctionTypeEnum.BIND,
                    ErrorTypeEnum.REVISE_STATUS_ERROR);
            throw new CustomerBizException(errorCode, "Trf source is OrderToTrf. Other Orders cannot be bound to this Trf. ");
        }

        TrfImportResult trfImportResult;
        if (ObjectUtil.isNotNull(trfInfoPO) && !trfInfoPO.getStatus().equals(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus())) {
            trfImportResult = getTrfImportResult(bindTrfReq, trfInfoPO);
        } else {
            if(supportImportModes.contains(TrfImportMode.TRF_IMPORT_MODE_PULL)) {
                TrfImportReq trfImportReq = buildImportTrfReq(bindTrfReq);
                trfImportResult = importTrf(trfImportReq);
            } else {
                trfImportResult = getSgsTrfByTrfNo(bindTrfReq.getTrfNo());
            }
        }

        return trfImportResult;
    }

    /**
     * 构建导入trf请求
     * @param bindTrfReq
     * @return
     */
    public  TrfImportReq buildImportTrfReq(TrfBindReq bindTrfReq) {
        TrfImportReq trfImportReq = new TrfImportReq();
        /**
         * bindTrf场景下，imporrTrf 默认为pull模式
         */
        trfImportReq.setImportMode(TrfImportMode.TRF_IMPORT_MODE_PULL);
        BeanUtils.copyProperties(bindTrfReq, trfImportReq);
        return trfImportReq;
    }

    /**
     * 获取可以绑定的trf
     * @param bindTrfReq
     * @param trfInfoPO
     * @return
     */
    public TrfImportResult getTrfImportResult(TrfBindReq bindTrfReq, TrfInfoPO trfInfoPO) {
        TrfImportResult trfImportResult;
        Long trfId = trfInfoPO.getId();
        boolean canBind = trfDomainService.checkBind(trfId);
        if (canBind) {
            TrfDTO trfDTO = trfDomainService.getTrfBaseInfo(trfInfoPO.getTrfNo(), bindTrfReq.getRefSystemId());
            trfImportResult = JSONObject.parseObject(JSONObject.toJSONString(trfDTO), TrfImportResult.class);
        } else {
            String message = String.format("TRF is %s status is  %s and cannot be bind", trfInfoPO.getTrfNo(), TrfStatusEnum.getText(trfInfoPO.getStatus()));
            ErrorCode bindErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.BIND, ErrorTypeEnum.STATUSERROR);
            throw new CustomerBizException(bindErrorCode, ResponseCode.FAIL.getCode(), message);
        }
        return trfImportResult;
    }

    /**
     * 获取trf信息
     * @param bindTrfReq
     * @return
     */
    public TrfInfoPO getTrfInfoPO(TrfBindReq bindTrfReq) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.BIND, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(bindTrfReq, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The reuest is requied");
        ErrorAssert.notNull(bindTrfReq.getTrfNo(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "The trfNo is requied");
        ErrorAssert.notNull(bindTrfReq.getRefSystemId(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId cannot be null!");
        ErrorAssert.notNull(bindTrfReq.getSystemId(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "systemId cannot be null!");
        // 1.基本参数验证

        // 2.TRF
        TrfInfoPO trfInfoPO = trfDomainService.getTrfInfoByTrfNo(bindTrfReq.getTrfNo(), bindTrfReq.getRefSystemId());
        return trfInfoPO;
    }


    private void trfStatusCheck(Integer refSystemId, String trfNo) {
        TrfHeaderDOV2 dbTrf = trfDomainService.selectSimple(refSystemId, trfNo);
        // trf 状态校验
        if (dbTrf != null && !Objects.equals(dbTrf.getTrfStatus(), TrfStatusEnum.New.getStatus())) {
            throw new BizException(ResponseCode.INTERNAL_SERVER_ERROR, StrUtil.format("TRF {} status is {} and cannot be import", dbTrf.getTrfNo(), com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.getTextEn(dbTrf.getTrfStatus())));
        }
    }

    public TrfNoDTO orderToTrf(OrderToTrfReq orderToTrfReq) {
        log.info("sci orderToTrf ,request:[{}]", JSON.toJSONString(orderToTrfReq));
        /**
         * 1、参数合法性校验
         * 2、根据场景 作数据准备（含默认赋值逻辑 及 进一步数据校验）
         */
        validateOrderToTrf(orderToTrfReq);

        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, BizScenario.DEFAULT_USE_CASE, getScenarioValue(orderToTrfReq.getHeader().getRefSystemId()));

        //执行orderToTrf
        return extensionExecutor.execute(
                OrderToTrfActionExtPt.class,
                bizScenario,
                orderToTrfActionExtPt -> transactionTemplate.execute(transactionStatus -> {
                            TrfNoDTO trfNoDTO = orderToTrfActionExtPt.orderToTrf(orderToTrfReq);
                            return trfNoDTO;
                        }
                )
        );
    }

    private String getScenarioValue(Integer refSystemId) {
        Integer integer = RefSystemIdAdapter.map.get(refSystemId);
        return Func.isEmpty(integer) ? Func.toStr(refSystemId) : Func.toStr(integer);
    }

    public TrfBaseResult unbindTrf(TrfUnbindReq unbindReq) {

        //校验请求参数
        validParamForUnbindTrf(unbindReq);

        List<Long> trfIdList = new ArrayList<>();
        Map<Long, TrfHeaderDTO> trfMap = new HashMap<>();

        unbindReq.getTrfList().forEach(trf -> {
            TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trf.getRefSystemId(), trf.getTrfNo());
            Assert.notNull(trfInfoPO, "Trf not Found!");
            trfMap.put(trfInfoPO.getId(), trf);
            trfIdList.add(trfInfoPO.getId());
        });

        //先整体检查trf List是否可以解绑
        trfIdList.forEach(trfId -> {
            Boolean canUnbind = trfDomainService.checkUnbind(trfId, unbindReq.getSystemId(), unbindReq);
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
            ErrorAssert.isTrue(canUnbind, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "Trf-Order can't unbind.");
        });

        TrfOrderDOV2 trfOrderDOV2 = convertToTrfOrderDO(unbindReq);

        //解绑trf-order
        trfIdList.forEach(trfId -> {
            TrfInfoPO trfInfoPO = trfDomainService.selectByTrfId(trfId);

            transactionTemplate.execute(tranStatus -> {
                if (TrfSourceType.Order2TRF.getSourceType().equals(trfInfoPO.getSource())) {
                    //如果是Order2Trf，则直接逻辑删除
                    trfDomainService.deleteTrf(trfId);
                } else {
                    TrfStatusControlDO trfStatusControlDO = TrfSyncConvertor.toUnbindTrfStatusControlDO(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId(), trfOrderDOV2);

                    trfDomainService.unbind(trfStatusControlDO);
                }
                return tranStatus;
            });

            fireUnbindEvent(unbindReq, trfId, trfMap, trfInfoPO);

            TrfInfoPO trfInfoPONew = trfDomainService.selectByTrfId(trfId);
            maybeFireStatusEvent(unbindReq, trfId, trfInfoPONew, trfInfoPO, trfMap);
        });


        return new TrfBaseResult();
    }

    private void maybeFireStatusEvent(TrfUnbindReq unbindReq, Long trfId, TrfInfoPO trfInfoPONew, TrfInfoPO trfInfoPO, Map<Long, TrfHeaderDTO> trfMap) {
        if (Func.isNotEmpty(trfInfoPONew) &&
                Objects.equals(trfInfoPONew.getActiveIndicator(), ActiveIndicatorEnum.Active.getStatus()) &&
                !Objects.equals(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus(), trfInfoPONew.getStatus())
       ) {
           Integer oldTrfStatus = trfInfoPO.getStatus();
           Integer newTrfStatus = trfInfoPONew.getStatus();
           if (!Objects.equals(oldTrfStatus, newTrfStatus)) {
               if (Objects.equals(oldTrfStatus, TrfStatusEnum.Revise.getStatus())) {
                   oldTrfStatus = TrfStatusEnum.Testing.getStatus();
               }
               while (true) {
                   oldTrfStatus += 1;
                   if (oldTrfStatus > newTrfStatus) {
                       break;
                   }
                   TrfEvent event = EventUtils.newTrfEvent(oldTrfStatus);
                   SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
                   TrfHeaderDTO header = trfMap.get(trfId);
                   TrfFullDTO trfFullDTO = new TrfFullDTO();
                   trfFullDTO.setHeader(header);
                   trfFullDTO.setTrfList(Arrays.asList(header));
                   event.setPayload(trfFullDTO);
                   event.setTriggerBy(TrfEventTriggerBy.PREORDER);
                   event.setRefSystemId(header.getRefSystemId());
                   event.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                   event.setSystemId(unbindReq.getSystemId());
                   event.setSourceId(requestContext.getRequestId());
                   event.setTrfNo(header.getTrfNo());
                   event.setStatusFrom(oldTrfStatus);
                   event.setStatusTo(newTrfStatus);
                   applicationEventPublisher.publishEvent(event);
               }
           }
       }
    }

    private void fireUnbindEvent(TrfUnbindReq unbindReq, Long trfId, Map<Long, TrfHeaderDTO> trfMap, TrfInfoPO trfInfoPO) {
        SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
        TrfHeaderDTO header = trfMap.get(trfId);
//        sgsMartService.unbindSgsmartTrf(unbindReq.getSystemId(),header.getTrfNo(),ProductLineContextHolder.getProductLineCode(),unbindReq.getOrder());
        TrfEvent event = new TrfUnBindEvent(TrfEventTriggerBy.PREORDER);
        TrfFullDTO trfFullDTO = new TrfFullDTO();
        trfFullDTO.setHeader(header);
        trfFullDTO.setTrfList(Arrays.asList(header));
        trfFullDTO.setOrderList(Arrays.asList(unbindReq.getOrder()));
        event.setPayload(trfFullDTO);
        event.setTriggerBy(TrfEventTriggerBy.PREORDER);
        event.setRefSystemId(header.getRefSystemId());
        event.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        event.setSystemId(unbindReq.getSystemId());
        event.setSourceId(requestContext.getRequestId());
        event.setTrfNo(header.getTrfNo());
        event.setStatusFrom(trfInfoPO.getStatus());
        event.setStatusTo(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus());
        applicationEventPublisher.publishEvent(event);
    }

    private TrfOrderDOV2 convertToTrfOrderDO(TrfUnbindReq trfUnbindReq) {
        TrfOrderDOV2 trfOrderDOV2 = new TrfOrderDOV2();

        trfOrderDOV2.setSystemId(trfUnbindReq.getSystemId());
        trfOrderDOV2.setOrderNo(trfUnbindReq.getOrder().getOrderNo());
        trfOrderDOV2.setRealOrderNo(trfUnbindReq.getOrder().getRealOrderNo());
        return trfOrderDOV2;
    }

    public TrfSyncResult syncTrf(TrfSyncReq syncReq) {
        log.info("sci syncTrf ,request:[{}]", JSON.toJSONString(syncReq));
        checkSyncTrfData(syncReq);

        syncTrfPreHandle(syncReq);

        //基本参数验证
        dfvHelper.validateParams(syncReq, DFV_CODE_FUNC_LEVEL_SYNC_TRF);

        //by customer参数校验
        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, BizScenario.DEFAULT_USE_CASE, syncReq.getAction());
        extensionExecutor.executeVoid(SyncActionValidatorExtPt.class, bizScenario, syncActionValidatorExtPt -> syncActionValidatorExtPt.validate(syncReq));


        //执行同步Event消息
        try {
            return extensionExecutor.execute(
                    SyncActionExtPt.class,
                    bizScenario,
                    syncActionExtPt -> syncActionExtPt.syncTrf(syncReq)
            );
        } finally {
            SyncTrfContextHolder.clean();
        }

    }

    public void syncTrfPreHandle(TrfSyncReq syncReq) {

        //SCI-1363 SCI-1634
        if (trfBizConfig.getSystemIdSetOfUseTrfInfoRefSystemIds().contains(syncReq.getSystemId())) {
            Optional.ofNullable(syncReq.getHeader())
                    .map(TrfSyncHeaderDTO::getTrfList)
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(trfList -> {
                        List<String> trfNoList = trfList.stream()
                                .map(TrfHeaderDTO::getTrfNo)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        Map<String, Integer> refSystemIdMap = trfDomainService.selectByTrfNos(trfNoList).stream()
                                .collect(Collectors.toMap(TrfInfoPO::getTrfNo, TrfInfoPO::getRefSystemId, (v1, v2) -> v1));
                        syncReq.getHeader().setRefSystemId(refSystemIdMap.values().stream().findFirst().orElse(syncReq.getHeader().getRefSystemId()));
                        trfList.forEach(trfHeaderDTO -> {
                            Integer refSystemIdFromDb = refSystemIdMap.getOrDefault(trfHeaderDTO.getTrfNo(), trfHeaderDTO.getRefSystemId());
                            log.info("Trf {} rewrite refSystemId from {} to {}", trfHeaderDTO.getTrfNo(), trfHeaderDTO.getRefSystemId(), refSystemIdFromDb);
                            trfHeaderDTO.setRefSystemId(refSystemIdFromDb);
                        });
                    });
        }

        // 如果body中的systemId为空则使用请求头中systemId，反之则不
        if (Func.isNotEmpty(syncReq.getOrder()) && Func.isEmpty(syncReq.getOrder().getSystemId())) {
            syncReq.getOrder().setSystemId(syncReq.getSystemId());
        }


        Boolean needNormed = Optional.ofNullable(syncReq.getHeader())
                .map(TrfSyncHeaderDTO::getRefSystemId)
                .map(scheduledWhiteList::allowNormDffData)
                .orElse(false);
        // 前置处理
        if (needNormed) {
            dffConvertor.convertDffLabCode(syncReq.getOrder().getProductList(), syncReq.getOrder().getSampleList());
        }

    }

    public static void checkSyncTrfData(TrfSyncReq syncReq) {
        //基本参数验证
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(syncReq, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "syncTrf request is null");
        ErrorAssert.notNull(syncReq.getAction(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "syncTrf request action is blank");
        // 临时方案，增加对OTS及土耳其reportStatus赋默认值203
        List<TrfReportDTO> reportList = syncReq.getReportList();
        if (Func.isNotEmpty(reportList) && (Objects.equals(syncReq.getSystemId(), 36) || Objects.equals(syncReq.getSystemId(), 39))) {
            reportList.forEach(l -> {
                if (Func.isEmpty(l.getReportStatus())) {
                    l.setReportStatus(ReportStatusEnum.Approved.getCode());
                }
            });
        }
        // SCI-900
        if (Func.isNotEmpty(syncReq.getHeader().getRefSystemId()) && Objects.equals(syncReq.getHeader().getRefSystemId(), 1)) {
            syncReq.getHeader().setRefSystemId(RefSystemIdEnum.SGSMart.getRefSystemId());
        }
    }


    public TrfBaseResult syncReportToTrf(TrfSyncReq trfSyncReq) {
        log.info("sci syncReportToTrf ,request:[{}]", JSON.toJSONString(trfSyncReq));

        //基本参数验证
//        BizScenario bizScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID, BizScenario.DEFAULT_USE_CASE, syncReq.getAction());
//
//        //基本参数验证
//        dfvHelper.validateParams(syncReportToTrfReq, DFV_CODE_FUNC_LEVEL_SYNC_TRF);

//        //by customer参数校验
//        extensionExecutor.executeVoid(SyncActionValidatorExtPt.class, bizScenario, syncActionValidatorExtPt -> syncActionValidatorExtPt.validate(syncReq));

        //根据业务需求校验业务逻辑
        checkSyncReportToTrf(trfSyncReq);

        //如果需要，调用RD 保存ReportData
        saveReportDataForDeliveryReport(trfSyncReq);

        //检查RD数据的有效
        checkRdReportExist(trfSyncReq);

        //queryTrfReportRel
        //exist(): updateTrfReport()
        //not Exist():buildTrfReport()
        trfSyncReq.getHeader().getTrfList().forEach(trfHeaderDTO -> {
            TrfInfoPO trfInfoPO = trfDomainService.getTrfInfoByTrfNo(trfHeaderDTO.getTrfNo(), trfSyncReq.getHeader().getRefSystemId());

            trfSyncReq.getReportList().forEach(trfReportDTO -> {
                updateTrfReportRel(trfInfoPO, trfSyncReq.getOrder(), trfReportDTO);
            });
        });

        //Trf.Report.status = approved|| completed && Trf.Report.deliveryFlag=new
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.isTrue(validateTreReportStatusAndTrfDeliveryFlag(trfSyncReq), errorCode, ResponseCode.FAIL.getCode(), "The Trf.Report status||deliveryFlag is invalid.");
        List<TrfReportDOV2> trfReportList = trfDomainService.queryTrfReport(trfSyncReq.getSystemId(), trfSyncReq.getOrder().getOrderNo());
        //过滤掉deliveryFlag != New的
        trfReportList = trfReportList.stream().filter(trfReportDOV2 -> DeliveryFlagEnum.NEW.getCode() == trfReportDOV2.getDeliveryFlag()).collect(Collectors.toList());

        SyncTrfContextHolder.getSyncTrfContext().setIncrReportList(trfReportList);

        deliveryReport(trfSyncReq);

        return new TrfBaseResult();
    }

    public void deliveryReport(TrfSyncReq syncReq) {
        TrfEvent event = new TrfDeliveryReportEvent(TrfEventTriggerBy.PREORDER);
        TrfFullDTO trfDOParam = TrfConvertor.toTrfFullDTO(syncReq.getHeader().getTrfList().get(0), syncReq);
        event.setPayload(trfDOParam);
        event.setTriggerBy(TrfEventTriggerBy.PREORDER);
        event.setRefSystemId(syncReq.getHeader().getRefSystemId());
        event.setProductLineCode(Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? syncReq.getProductLineCode() : ProductLineContextHolder.getProductLineCode());
        event.setSystemId(syncReq.getSystemId());
        event.setSourceId(syncReq.getRequestId());
        event.setTrfNo(syncReq.getHeader().getTrfList().get(0).getTrfNo());
        event.setIncrReportList(cn.hutool.core.util.ObjectUtil.defaultIfNull(SyncTrfContextHolder.getSyncTrfContext().getIncrReportList(), Collections.emptyList()));
        event.setOperator(syncReq.getOperator());
        event.setOperationTime(syncReq.getOperationTime());
        applicationEventPublisher.publishEvent(event);
    }

    public TrfReturnResult returnTrf(TrfRemoveReq reqObject, @NotNull IdentityEnum identity) {
        log.info("sci returnTrf ,request:[{}] ,identity:{}", JSON.toJSONString(reqObject), identity);
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(reqObject, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The request is required");
        ErrorAssert.notNull(reqObject.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        ErrorAssert.notNull(reqObject.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        ErrorAssert.notNull(reqObject.getReturnType(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("TRF {} can not be return，please upload return type", reqObject.getTrfNo()));

//        TrfDOV2 trfDO = trfDomainService.getTrfBaseInfo(reqObject.getTrfNo(), reqObject.getRefSystemId());
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(reqObject.getRefSystemId(), reqObject.getTrfNo());
        if (null == trfInfoPO) {
            returnUnImportedTrf(reqObject.getTrfNo(), reqObject.getRefSystemId());
            return new TrfReturnResult();
        }

        boolean isCustomerCall = IdentityEnum.CUSTOMER == identity;

        TrfStatusControlDO statusControlDO = new TrfStatusControlDO();
        statusControlDO.setAction(isCustomerCall ?
                TrfActionEnum.CUSTOMER_RETURN_TRF.getCode() : TrfActionEnum.PREORDER_RETURN_TRF.getCode());
        statusControlDO.setRefSystemId(reqObject.getRefSystemId());
        statusControlDO.setTrfNo(reqObject.getTrfNo());
        statusControlDO.setReasonType(reqObject.getReturnType());
        statusControlDO.setReasonRemark(reqObject.getReturnReason());


        TrfHeaderDOV2 trfHeader = transactionTemplate.execute(transactionStatus -> {

            trfInfoExtMapper.updateTrfActiveIndicator(statusControlDO.getRefSystemId(), statusControlDO.getTrfNo());

            return trfDomainService.returnTrf(statusControlDO);

        });

        TrfEvent trfEvent = isCustomerCall ? new TrfCustomerReturnEvent() : new TrfPreOrderReturnEvent();
        trfEvent.setRefSystemId(trfHeader.getRefSystemId());
        trfEvent.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        trfEvent.setSystemId(trfHeader.getSystemId());
        trfEvent.setSourceId(reqObject.getRequestId());
        trfEvent.setTrfNo(trfHeader.getTrfNo());
        applicationEventPublisher.publishEvent(trfEvent);

        return new TrfReturnResult();
    }

    public TrfCancelResult cancelTrf(TrfCancelReq reqObject, @NotNull IdentityEnum identity) {
        log.info("sci cancelTrf ,request:[{}] ,identity:{}", JSON.toJSONString(reqObject), identity);
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(reqObject, errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The request is required");
        ErrorAssert.notNull(reqObject.getRefSystemId(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        ErrorAssert.notNull(reqObject.getTrfNo(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        ErrorAssert.notNull(reqObject.getCancelType(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("TRF {} can not be cancel，please upload cancel type", reqObject.getTrfNo()));
        ErrorAssert.notEmpty(reqObject.getCancelReason(), errorCode, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("TRF {} can not be cancel，please upload cancel reason", reqObject.getTrfNo()));

//        TrfDOV2 trfDO = trfDomainService.getTrfBaseInfo(reqObject.getTrfNo(), reqObject.getRefSystemId());
        TrfInfoPO trfPO = trfDomainService.selectByTrfNo(reqObject.getRefSystemId(), reqObject.getTrfNo());
        if (trfPO == null) {
            cancelUnImportedTrf(reqObject.getTrfNo(), reqObject.getRefSystemId());
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "The Trf Not Existed!");
        }

        TrfStatusControlDO statusControlDO = new TrfStatusControlDO();
        if (IdentityEnum.CUSTOMER == identity) {
            statusControlDO.setAction(TrfActionEnum.CUSTOMER_CANCEL_TRF.getCode());
        } else if (IdentityEnum.PREORDER == identity) {
            statusControlDO.setAction(TrfActionEnum.PREORDER_CANCEL_TRF.getCode());
        } else if (IdentityEnum.TODOLIST == identity) {
            statusControlDO.setAction(TrfActionEnum.TODOLIST_CANCEL_TRF.getCode());
        }
        statusControlDO.setRefSystemId(reqObject.getRefSystemId());
        statusControlDO.setTrfNo(reqObject.getTrfNo());
        statusControlDO.setReasonType(reqObject.getCancelType());
        statusControlDO.setReasonRemark(reqObject.getCancelReason());

        TrfHeaderDOV2 trfHeader = transactionTemplate.execute(transactionStatus -> trfDomainService.cancelTrf(statusControlDO));

        TrfEvent trfEvent = null;
        if (IdentityEnum.CUSTOMER == identity) {
            trfEvent = new TrfCustomerCancelEvent();
        } else if (IdentityEnum.PREORDER == identity) {
            trfEvent = new TrfPreOrderCancelEvent();
        } else if (IdentityEnum.TODOLIST == identity) {
            trfEvent = new TrfTodolistCancelEvent();
        }
        trfEvent.setRefSystemId(trfHeader.getRefSystemId());
        trfEvent.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        trfEvent.setSystemId(trfHeader.getSystemId());
        trfEvent.setSourceId(reqObject.getRequestId());
        trfEvent.setTrfNo(trfHeader.getTrfNo());

        String[] samplePhotos = reqObject.getSamplePhotos();
        String materialItem = reqObject.getMaterialItem();
        TrfFullDTO trfFullDTO = new TrfFullDTO();
        trfEvent.setPayload(trfFullDTO);
        TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
        trfFullDTO.setHeader(trfHeaderDTO);
        TrfOtherDTO otherDTO = new TrfOtherDTO();
        TrfCancelDTO cancelDTO = new TrfCancelDTO();
        otherDTO.setCancel(cancelDTO);
        cancelDTO.setSamplePhotos(samplePhotos);
        cancelDTO.setCancelType(reqObject.getCancelType());
        cancelDTO.setCancelReason(reqObject.getCancelReason());
        cancelDTO.setMaterialItem(materialItem);
        trfHeaderDTO.setOthers(otherDTO);
        applicationEventPublisher.publishEvent(trfEvent);

        return new TrfCancelResult();

    }

    private void checkSyncReportToTrf(TrfSyncReq trfSyncReq) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(trfSyncReq, errorCode, ResponseCode.PARAM_MISS.getCode(), "The request is required");
        ErrorAssert.notNull(trfSyncReq.getHeader(), errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), "The Request.header is required.");
        ErrorAssert.notNull(trfSyncReq.getHeader().getRefSystemId(), errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), "The Request.header.refSystemId is required.");
        ErrorAssert.isTrue(CollectionUtil.isNotEmpty(trfSyncReq.getHeader().getTrfList()), errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), "The Request.header.trfList is required.");
        ErrorAssert.notNull(trfSyncReq.getOrder(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The request.order is required");
        ErrorAssert.isTrue(CollectionUtil.isNotEmpty(trfSyncReq.getReportList()), errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), "The Request.reportList is required.");

        trfSyncReq.getHeader().getTrfList().forEach(trf -> {
            TrfInfoPO trfInfoPO = trfDomainService.getTrfInfoByTrfNo(trf.getTrfNo(), trfSyncReq.getHeader().getRefSystemId());
            ErrorAssert.isTrue(validateTrfExist(trfInfoPO.getRefSystemId(), trfInfoPO.getTrfNo()), errorCode, ResponseCode.FAIL.getCode(), "The Request.trfList is not valid.");
            ErrorAssert.isTrue(validateTrfStatusForSyncReportToTrf(trfInfoPO.getStatus()), errorCode, ResponseCode.FAIL.getCode(), "The Trf.Status is invalid");
            ErrorAssert.isTrue(trfInfoPO.getPendingFlag() == PendingFlagEnum.UnPending.getType(), errorCode, ResponseCode.FAIL.getCode(), "The Trf.PendingFlag is invalid");
            ErrorAssert.isTrue(validateTrfOrderRel(trfInfoPO.getTrfNo(), trfInfoPO.getRefSystemId(), trfSyncReq.getOrder().getOrderNo(),trfSyncReq.getOrder().getRealOrderNo()), errorCode, ResponseCode.FAIL.getCode(), "The Trf.Order is invalid");
        });

        // Config.RefSystemId.Report.SendMode = RealTime
        //TODO Config 整理完以后，ParamCode统一定义常量 kevin@20231123
        String customerReportDeliveryMode = getConfig(ConfigKeyConstant.CUSTOMER_REPORT_SYNC_MODE, trfSyncReq.getHeader().getRefSystemId());
        if (CustomerReportDeliveryMode.REAL_TIME.getCode() != Integer.valueOf(customerReportDeliveryMode)) {
            log.info("sci syncReportToTrf : Don't map valid reportDeliveryMode,refSystemId:[{}] ,requestId:{}", trfSyncReq.getHeader().getRefSystemId(), trfSyncReq.getRequestId());
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "Don't map valid reportDeliveryMode");
        }
    }

    public void validateParamForOrderToTrf(OrderToTrfReq orderToTrfReq) {
        //基本参数验证
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(orderToTrfReq, errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request is required");
        ErrorAssert.notNull(orderToTrfReq.getHeader(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request.header is required");
        ErrorAssert.notNull(orderToTrfReq.getHeader().getRefSystemId(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request.header.refSystemId is required");
        ErrorAssert.notNull(orderToTrfReq.getOrder(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request.order is required");
        ErrorAssert.notNull(orderToTrfReq.getHeader().getLab(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request.header.lab is required");
        ErrorAssert.notNull(orderToTrfReq.getHeader().getLab().getLabCode(), errorCode, ResponseCode.PARAM_MISS.getCode(), "The Request.header.lab.labCode is required");
        ErrorAssert.isTrue(Objects.equals(orderToTrfReq.getHeader().getLab().getLabCode(), orderToTrfReq.getLabCode()), errorCode, ResponseCode.PARAM_VALID_ERROR.getCode(), "Request header and body labCode atypism");
    }

    private void validateOrderToTrf(OrderToTrfReq orderToTrfReq) {
        /**
         * 校验请求的参数准入
         */
        validateParamForOrderToTrf(orderToTrfReq);

        /**
         *  1、优先取Req.header.trfNo
         *  2、如果Req.header.trfNo为空，则根据refSystemId读取配置：trfNoType（？）
         *  2.1 call CustomerSystem 获得TrfNo  - - 如果失败，则异常退出
         *  2.2 trfNo = orderNo
         */
        validateTrfNoForOrderToTrf(orderToTrfReq);

        /**
         * 1、先根据refSystemId判断integerationLevel；
         *  N_ORDER_MODE : 一个 (orderNo) 只能创建一个Trf
         *  N_TRF_MODE : 一个(OrderNo + TrfNo) 只能创建一个Trf
         */
        validateTrfExistForOrderToTrf(orderToTrfReq);

        filterData(orderToTrfReq);

        // CustomerGroupCode 映射 RefSystemId
        Integer refSystemId = mappingRefSystemId(orderToTrfReq.getCustomerList(), orderToTrfReq.getProductLineCode(), TrfSourceType.Order2TRF.getDescription());
        // TODO 此处待所有执行系统都传递正确refSystemId后需要修复
        if (Func.isNotEmpty(refSystemId) && !Objects.equals(SgsSystem.OTS.getSgsSystemId(), orderToTrfReq.getSystemId())) {
            orderToTrfReq.getHeader().setRefSystemId(refSystemId);
        }
    }

    public void filterData(OrderToTrfReq orderToTrfReq) {
        filterAttachmentsByStatus(orderToTrfReq, ActiveType.Enable.getStatus());
    }

    private void filterAttachmentsByStatus(OrderToTrfReq orderToTrfReq, Integer targetStatus) {
        filterAttachmentList(orderToTrfReq.getAttachmentList(), targetStatus);
        filterAttachmentList(orderToTrfReq.getOrder().getAttachmentList(), targetStatus);
    }

    private void filterAttachmentList(List<TrfFileDTO> attachmentList, Integer targetStatus) {
        if (attachmentList != null && !attachmentList.isEmpty()) {
            attachmentList.removeIf(file -> !Objects.equals(file.getToCustomerFlag(), targetStatus));
        }
    }

    public CustomerConfigRsp getCustomerConfig(GetCustomerConfigReq getConfigReq) {
        // 1. 参数校验
        Assert.notNull(getConfigReq, "请求参数不能为空！");
        Assert.notBlank(getConfigReq.getCustomerGroupCode(), "customerGroupCode不能为空！");
        Assert.notBlank(getConfigReq.getBuCode(), "buCode cannot null！");
        Assert.notBlank(getConfigReq.getServiceUnit(), "serviceUnit cannot null！");

        // 2. 初始化响应对象
        CustomerConfigRsp customerConfigRsp = new CustomerConfigRsp();
        customerConfigRsp.setHeader(getConfigReq);

        // 3. 查询配置信息
        Optional<ConfigInfo> rawConfig = configClient.getCustomerConfig(getConfigReq.getBuCode(), getConfigReq.getCustomerGroupCode(), getConfigReq.getCustomerNo());
        if ( ! rawConfig.isPresent()) {
            return customerConfigRsp;
        }

        ConfigInfo customerConfig = rawConfig.get();

        // 5. 解析配置并验证
        CustomerGeneralConfig customerGeneralConfig = JSON.parseObject(customerConfig.getConfigValue(), CustomerGeneralConfig.class);

        // 验证TrfSource
        if (Func.isNotEmpty(customerGeneralConfig) && Func.isNotEmpty(getConfigReq.getTrfSource())) {
            List<String> source = customerGeneralConfig.getSource();
            if (Func.isNotEmpty(source) && !source.contains(getConfigReq.getTrfSource())) {
                return customerConfigRsp;
            }
        }

        customerConfigRsp.setCustomerGeneralConfig(customerGeneralConfig);
        return customerConfigRsp;
    }

    public Integer mappingRefSystemId(List<TrfCustomerDTO> customerDTOList, String productLineCode, String model) {

        Optional<TrfCustomerDTO> buyer = Optional.ofNullable(customerDTOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(customers -> customers.stream().filter(l -> Objects.equals(CustomerUsage.Buyer.getUsage(), l.getCustomerUsage())).findFirst());
        // 1. 空值检查
        if (! buyer.isPresent()) {
            return null;
        }

        // 2. 获取买家客户信息
        TrfCustomerDTO trfCustomerDTO = buyer.get();

        // 3. 客户信息有效性检查
        if (Func.isBlank(trfCustomerDTO.getCustomerGroupCode()) && Func.isEmpty(trfCustomerDTO.getBossNo())) {
            return null;
        }

        // 4. 如果没有CustomerGroupCode，尝试通过BossNo获取
        if (Func.isBlank(trfCustomerDTO.getCustomerGroupCode())) {
            CustomerAndGroupInfoReq req = new CustomerAndGroupInfoReq();
            req.setBuCode(productLineCode);
            req.setCustomerNumber(trfCustomerDTO.getBossNo().toString());
            CustomerAndGroupInfoRsp customerAndGroupInfo = customerClient.getCustomerAndGroupInfo(req);
            if (Func.isNotEmpty(customerAndGroupInfo) && Func.isNotEmpty(customerAndGroupInfo.getRows())) {
                trfCustomerDTO.setCustomerGroupCode(customerAndGroupInfo.getRows().get(0).getCustomerGroupCode());
            }
        }

        // 5. CustomerGroupCode有效性检查
        if (Func.isBlank(trfCustomerDTO.getCustomerGroupCode())) {
            return null;
        }

        // 6. 构建配置查询请求
        ConfigGetReq getCustomerConfigReq = new ConfigGetReq();
        getCustomerConfigReq.setCustomerGroup(trfCustomerDTO.getCustomerGroupCode());
        getCustomerConfigReq.setProductLine(productLineCode);

        // 7. 查询配置信息
        Optional<ConfigInfo> rawConfig = configClient.getCustomerConfig(
                productLineCode,
                trfCustomerDTO.getCustomerGroupCode(),
                Optional.ofNullable(trfCustomerDTO.getBossNo()).map(Object::toString).orElse(null)
        );

        // 8. 获取第一条配置记录
        if (!rawConfig.isPresent()) {
            return null;
        }
        ConfigInfo customerConfig = rawConfig.get();

        // 9. 解析配置并验证source
        CustomerGeneralConfig customerGeneralConfig = JSON.parseObject(customerConfig.getConfigValue(), CustomerGeneralConfig.class);
        List<String> source = customerGeneralConfig.getSource();
        if (Func.isNotEmpty(source) && Func.isNotBlank(model) && !source.contains(model)) {
            return null;
        }

        // 10. 返回identityId
        return Integer.valueOf(customerConfig.getIdentityId());
    }

    /**
     * 1、优先取Req.header.trfNo
     * 2、如果Req.header.trfNo为空，则根据refSystemId读取配置：trfNoType（？）
     * 2.1 call CustomerSystem 获得TrfNo  - - 如果失败，则异常退出
     * 2.2 trfNo = orderNo
     *
     * @param orderToTrfReq
     */
    public void validateTrfNoForOrderToTrf(OrderToTrfReq orderToTrfReq) {
        if (Func.isNotBlank(orderToTrfReq.getHeader().getTrfNo())) {
            return;
        }

        if (Func.equals(RefSystemIdEnum.SGSMart.getRefSystemId(), RefSystemIdAdapter.map.get(orderToTrfReq.getHeader().getRefSystemId()))) {
            // 如果是Sgsmart，则需要call Sgsmart 创建Trf
            //TODO 暂时不处理，因为后面路由策略会走到独立的SgsmartOrderToTrfActionExtPt
        } else {
            orderToTrfReq.getHeader().setTrfNo(orderToTrfReq.getOrder().getOrderNo());
        }
    }

    public void validateTrfExistForOrderToTrf(OrderToTrfReq orderToTrfReq) {
        Integer refSystemId = orderToTrfReq.getHeader().getRefSystemId();

        // 1、TrfNo +  RefSystemId 唯一
        if (Func.isNotBlank(orderToTrfReq.getHeader().getTrfNo())) {
            //Assert.isTrue(!validateTrfExist(refSystemId, orderToTrfReq.getHeader().getTrfNo()), ResponseCode.FAIL.getCode(), "The TrfNo is Exist.");
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATACONFLICT);
            ErrorAssert.isTrue(!validateTrfExist(refSystemId, orderToTrfReq.getHeader().getTrfNo()), errorCode, ResponseCode.FAIL.getCode(), "The TrfNo is Exist.");
        }

        // 根据RefSystemId 获得 IntegrationLevel
        Integer integrationLevel = trfDomainService.calcIntegrationLevel(refSystemId, orderToTrfReq.getBuCode());

        // 如果IntegrationLevelEnum.N_ORDER_MODE : 则一个order只能创建一个Trf
        if (Objects.equals(TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule(), integrationLevel)) {
            // 2、orderNo 不能已经绑定了其他TrfNo
            List<TrfOrderDOV2> trfOrderDOV2List = trfDomainService.getTrfOrderList(orderToTrfReq.getOrder().getSystemId(), orderToTrfReq.getOrder().getOrderNo());
            // 应该为空
            //Assert.isTrue(Func.isEmpty(trfOrderDOV2List), ResponseCode.FAIL.getCode(), "The OrderNo has bind another Trf.");
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.STATUSERROR);
            ErrorAssert.isTrue(Func.isEmpty(trfOrderDOV2List), errorCode, ResponseCode.FAIL.getCode(), "The OrderNo has bind another Trf.");
        }

    }

    private Boolean validateTreReportStatusAndTrfDeliveryFlag(TrfSyncReq trfSyncReq) {
        //请求的reportNoList
        List<String> reqReportNoList = trfSyncReq.getReportList().stream().map(TrfReportDTO::getReportNo).collect(Collectors.toList());

        //请求OrderNo 下面的所有TrfReport
        List<TrfReportDOV2> trfReportDOV2List = trfDomainService.queryTrfReport(trfSyncReq.getSystemId(), trfSyncReq.getOrder().getOrderNo());

        //过滤得到请求中TrfReport结果集
        List<TrfReportDOV2> validTrfReportList = trfReportDOV2List.stream().filter(trfReport -> reqReportNoList.contains(trfReport.getReportNo())).collect(Collectors.toList());

        //判断是否所有状态为Approved or Completed
        TrfReportDOV2 invalidTrfReport = validTrfReportList.stream().filter(trfReport -> !Arrays.asList(ReportStatusEnum.Approved.getCode(), ReportStatusEnum.Completed.getCode()).contains(trfReport.getReportStatus())).findAny().get();
        if (invalidTrfReport != null) {
            return Boolean.FALSE;
        }

        //判断是否所有DeliveryFlag != new
        invalidTrfReport = validTrfReportList.stream().filter(trfReport -> Integer.valueOf(DeliveryFlagEnum.NEW.getCode()) != trfReport.getDeliveryFlag()).findAny().get();
        if (invalidTrfReport != null) {
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    private void updateTrfReportRel(TrfInfoPO trfIno, TrfOrderDTO trfOrderDTO, TrfReportDTO trfReportDTO) {
        TrfReportDOV2 trfReportDO = convertToTrfReportDO(trfIno, trfOrderDTO, trfReportDTO);
        trfDomainService.createTrfReport(trfIno, trfReportDO);
    }

    private void saveReportDataForDeliveryReport(TrfSyncReq trfSyncReq) {
        List<TrfHeaderDTO> trfList = trfSyncReq.getHeader().getTrfList();
        for (TrfHeaderDTO trfHeaderDTO : trfList) {
            TrfFullDTO trfDOParam = TrfConvertor.toTrfFullDTO(trfHeaderDTO, trfSyncReq);
            CheckReportExist reportExist = new CheckReportExist();
            boolean doEvalFlag = reportExist.doEval(trfDOParam);
            if (!doEvalFlag) {
                reportDataService.saveReportData(trfDOParam);
            }
        }
    }

    private void checkRdReportExist(TrfSyncReq trfSyncReq) {
        // ReportExist & Status = Approved||Completed
        BatchExistReportDataReq existReportDataReq = new BatchExistReportDataReq();
        existReportDataReq.setReportNos(trfSyncReq.getReportList().stream().map(TrfReportDTO::getReportNo).collect(Collectors.toList()));
        existReportDataReq.setSystemId(Long.valueOf(trfSyncReq.getSystemId()));
        existReportDataReq.setLabCode(trfSyncReq.getLabCode());
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATANOTFOUND);
        ErrorAssert.isTrue(reportDataClient.existReportData(existReportDataReq), errorCode, ResponseCode.FAIL.getCode(), "The Report not exist in RD.");

    }

    private TrfReportDOV2 convertToTrfReportDO(TrfInfoPO trfInfo, TrfOrderDTO trfOrderDTO, TrfReportDTO trfReportDTO) {
        TrfReportDOV2 trfReportDO = new TrfReportDOV2();
        trfReportDO.setReportNo(trfReportDTO.getReportNo());
        trfReportDO.setTrfId(trfInfo.getId());
        trfReportDO.setReportStatus(trfReportDTO.getReportStatus());
        trfReportDO.setOrderId(trfOrderDTO.getOrderId());
        trfReportDO.setOrderNo(trfOrderDTO.getOrderNo());
        trfReportDO.setSystemId(trfOrderDTO.getSystemId());
        trfReportDO.setLabId(trfInfo.getLabId());
        if (CollectionUtil.isNotEmpty(trfReportDTO.getTrfList())) {
            trfReportDO.setTrfList(new ArrayList<>());
            trfReportDTO.getTrfList().forEach(trfReferenceDTO -> {
                TrfReferenceDO trfReferenceDO = new TrfReferenceDO();
                trfReferenceDO.setTrfNo(trfReferenceDTO.getTrfNo());
                trfReportDO.getTrfList().add(trfReferenceDO);
            });
        }

        return trfReportDO;
    }

    private Boolean validateTrfExist(Integer refSystemId, String trfNo) {
        List<TrfInfoPO> trfList = trfDomainService.selectActiveAndNotCancelByTrfNo(trfNo);
        if (trfList.size() > 1) {
            return false;
        }
        if (!trfList.isEmpty()) {
            TrfInfoPO existingTrf = trfList.get(0);
            return Objects.equals(existingTrf.getRefSystemId(), refSystemId);
        }
        return false;
    }

    private Boolean validateTrfOrderRel(String trfNo, Integer refSystemId, String orderNo, String realOrderNo) {
        TrfOrderPO trfOrderPO = trfDomainService.getTrfOrder(trfNo, refSystemId, orderNo,realOrderNo);

        return trfOrderPO != null;
    }

    /**
     * ToEnquiry、ToOrder、Confirmed、Testing、Reporting、Revised
     *
     * @param trfStatus
     * @return
     */
    private Boolean validateTrfStatusForSyncReportToTrf(Integer trfStatus) {
        //TODO 现在Trf没有Reporting的状态定义，，等添加Reporting 后，此处需要增加
        return Arrays.asList(TrfStatusEnum.ToQuotation.getStatus(), TrfStatusEnum.ToOrder.getStatus(), TrfStatusEnum.Confirmed.getStatus(), TrfStatusEnum.Testing.getStatus(), TrfStatusEnum.Revise.getStatus())
                .contains(trfStatus);
    }

    private void validParamForUnbindTrf(TrfUnbindReq unbindReq) {
        /**
         * order不能为空，且orderNo不能为空
         * trfList不能为空，且trfNo、refSystemId不能为空
         */
        ErrorCode bindErrorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.UNBIND, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(unbindReq.getOrder(), bindErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "Order should not be null.");
        ErrorAssert.isTrue(CollectionUtil.isNotEmpty(unbindReq.getTrfList()), bindErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfList should not be null.");

        ErrorAssert.notBlank(unbindReq.getOrder().getOrderNo(), bindErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "OrderNo should not be blank.");
        unbindReq.getTrfList().forEach(trf -> ErrorAssert.notBlank(trf.getTrfNo(), bindErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "TrfNo should not be blank."));
        unbindReq.getTrfList().forEach(trf -> ErrorAssert.notNull(trf.getRefSystemId(), bindErrorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "RefSystemId should not be null."));
    }

    private void cancelUnImportedTrf(String trfNo, Integer refSystemId) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.CANCEL, ErrorTypeEnum.REQUESTNULL);
        checkParam(trfNo, refSystemId, errorCode);

        CustomerTrfInfoPO customerTrf = new CustomerTrfInfoPO();
        customerTrf.setTrfNo(trfNo);
        customerTrf.setRefSystemId(refSystemId);
        customerTrf.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());
        customerTrf.setModifiedBy(USER_DEFAULT);
        customerTrf.setModifiedDate(DateUtils.getNow());

        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystemId, Lists.newArrayList(trfNo));
        trfInfoExtMapper.updateCustomerTrf(customerTrf);
    }

    private void returnUnImportedTrf(String trfNo, Integer refSystemId) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.RETURN, ErrorTypeEnum.REQUESTNULL);
        checkParam(trfNo, refSystemId, errorCode);

        CustomerTrfInfoPO customerTrf = new CustomerTrfInfoPO();
        customerTrf.setTrfNo(trfNo);
        customerTrf.setRefSystemId(refSystemId);
        customerTrf.setActiveIndicator(ActiveIndicatorEnum.Inactive.getStatus());
        customerTrf.setModifiedBy(USER_DEFAULT);
        customerTrf.setModifiedDate(DateUtils.getNow());

        trfTodoInfoExtMapper.deleteTrfTodoInfo(refSystemId, Lists.newArrayList(trfNo));
        trfInfoExtMapper.updateCustomerTrf(customerTrf);
    }

    private void checkParam(String trfNo, Integer refSystemId, ErrorCode errorCode) {
        ErrorAssert.notNull(trfNo, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "trfNo should not be null");
        ErrorAssert.notNull(refSystemId, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "refSystemId should not be null");
    }

    @PostConstruct
    public void initCustomerTrfInvokerMap() {
        customerTrfInvokerList.forEach(customerTrfInvoker -> customerTrfInvokerMap.put(customerTrfInvoker.getName(), customerTrfInvoker));
        orderToTrfInvokerList.forEach(orderToTrfInvoker -> orderToTrfInvokerMap.put(orderToTrfInvoker.getName(), orderToTrfInvoker));
    }

    public TrfImportResult exportSgsTrfByTrfNo(TrfImportReq trfImportReq) {
        TrfDTO trfDTO = trfDomainService.getTrfBaseInfo(trfImportReq.getTrfNo(), trfImportReq.getRefSystemId());
        return JSONObject.parseObject(JSONObject.toJSONString(trfDTO), TrfImportResult.class);
    }

    public TrfImportResult getSgsTrfByTrfNo(String trfNo) {
        List<TrfInfoPO> trfInfoList = trfDomainService.selectActiveAndNotCancelByTrfNo(trfNo);
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.TRFDOMAIN, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        if (CollectionUtils.isEmpty(trfInfoList)) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "trf 不存在");
        }
        if(trfInfoList.size()!=1) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "to many trf by TrfNo:" + trfNo);
        }
        TrfInfoPO trfInfoPO = CollUtil.get(trfInfoList, 0);
        List<CustomerTrfInfoRsp> customerInfo = trfInfoExtMapper.getTrfInfoList(ImmutableList.of(trfInfoPO.getTrfNo()), true, ImmutableList.of());
        if (CollectionUtils.isEmpty(customerInfo)) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "trf 不存在");
        }
        if(customerInfo.size()!=1) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "to many trf by TrfNo:" + trfNo);
        }
        CustomerTrfInfoRsp customerTrfInfoRsp = CollUtil.get(customerInfo, 0);
        String content = customerTrfInfoRsp.getContent();
        TrfDTO trfDTO = customerTrfToSgsTrfService.customerTrfContentToSGSTrf(trfInfoPO.getRefSystemId(), content);
        return JSONObject.parseObject(JSONObject.toJSONString(trfDTO), TrfImportResult.class);
    }

    public String getConfig(GetConfigReq getConfigReq) {

        return configClient.getConfig(getConfigReq.getBuCode(),
                Integer.valueOf(getConfigReq.getRefSystemId()),
                getConfigReq.getConfigKey(),
                getConfigReq.getCustomerGroup(),
                getConfigReq.getCustomerNo());
    }

    public String getConfig(String paramCode, Integer refSystemId) {
        return configClient.getConfig(refSystemId, paramCode);
    }

    public Object syncQuotationToTrf(TrfSyncQuotationReq trfSyncQuotationReq) {
        TrfFullDTO trfFullDTO = TrfConvertor.toTrfFullDTO(trfSyncQuotationReq);
        trfFullDTO.setAction("SyncQuotationToTrf");

        BizScenario bizScenario = BizScenario.valueOf(trfFullDTO.getAction(), BizScenario.DEFAULT_USE_CASE, BizScenario.DEFAULT_SCENARIO);

        //执行同步Event消息
        try {
            extensionExecutor.execute(
                    DeliveryActionExtPt.class,
                    bizScenario,
                    deliveryActionExtPt -> deliveryActionExtPt.delivery(trfFullDTO)
            );
        } finally {
            SyncTrfContextHolder.clean();
        }

        TrfEvent event = new TrfDeliveryQuotationEvent(TrfEventTriggerBy.PREORDER);
        event.setOperator(trfSyncQuotationReq.getOperator());
        event.setOperationTime(trfSyncQuotationReq.getOperationTime());
        event.setOperatorEmail(trfSyncQuotationReq.getOperatorEmail());
        event.setPayload(trfFullDTO);
        event.setTriggerBy(TrfEventTriggerBy.PREORDER);
        event.setRefSystemId(trfSyncQuotationReq.getTrfList().get(0).getRefSystemId());
        event.setProductLineCode(trfSyncQuotationReq.getProductLineCode());
        event.setSystemId(trfSyncQuotationReq.getTrfList().get(0).getSystemId());
        event.setSourceId(trfSyncQuotationReq.getRequestId());
        event.setTrfNo(trfSyncQuotationReq.getTrfList().get(0).getTrfNo());
        applicationEventPublisher.publishEvent(event);

        return new CustomResult();
    }

    public Object sendQuotationStatus(TrfSyncQuotationStatusReq reqObject) {

        Assert.notNull(reqObject);
        Assert.notNull(reqObject.getData());

        TrfApproveDO trfApproveDO = TrfConvertor.toTrfApprovedDO(reqObject.getData());
        // TODO SCI-900
        if (Func.isNotEmpty(reqObject.getData().getRefSystemId()) && Objects.equals(reqObject.getData().getRefSystemId(), 1)) {
            reqObject.getData().setRefSystemId(2);
        }

        TrfInfoPO trfInfoByTrfNo = trfDomainService.getTrfInfoByTrfNo(reqObject.getData().getTrfNo(), reqObject.getData().getRefSystemId());
        if (Func.isEmpty(trfInfoByTrfNo)) {
            throw new BizException(StrUtil.format("The TrfNo：{} Not Exist!", reqObject.getData().getTrfNo()));
        }
        trfApproveDomainService.approve(trfInfoByTrfNo.getId(), ApproveObjectTypeEnum.Quotation.getCode(), trfApproveDO);
        TrfEvent event = new TrfQuotationApprovedEvent(TrfEventTriggerBy.CUSTOMER);
        event.setOperator(reqObject.getOperator());
        event.setOperationTime(reqObject.getOperationTime());
        event.setOperatorEmail(reqObject.getOperatorEmail());
        event.setPayload(reqObject);
        event.setTriggerBy(TrfEventTriggerBy.CUSTOMER);
        event.setRefSystemId(reqObject.getData().getRefSystemId());
        event.setProductLineCode(reqObject.getProductLineCode());
        event.setSystemId(reqObject.getSystemId());
        event.setSourceId(reqObject.getRequestId());
        event.setTrfNo(trfInfoByTrfNo.getTrfNo());
        applicationEventPublisher.publishEvent(event);
        return new CustomResult();
    }

    public CustomResult syncReviewConclusion(SyncReportToTrfReq req) {
        ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SCITRFBIZSERVICE, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
        ErrorAssert.notNull(req, errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "Request params cannot null！");
        ErrorAssert.notNull(req.getReportList(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "ReportList cannot null！");
        // todo sci-900
        if (req.getSystemId().equals(Constants.SYSTEM_SGSMART)) {
            ErrorAssert.notEmpty(req.getLabCode(), errorCode, ResponseCode.ILLEGAL_ARGUMENT.getCode(), "LabCode cannot be empty!");
        }
        List<TrfReportDTO> reports = req.getReportList();

        // 验证 ReportNo 不能为空或空白
        long count = reports.stream()
                .map(TrfReportDTO::getReportNo)
                .filter(Func::isBlank)
                .count();
        if (count > 0) {
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "ReportNo cannot null!");
        }
        // 创建一个 Map 用于存储 ReportNo 和对应的 TrfReportDTO
        Map<String, TrfReportDTO> reportMap = new HashMap<>();
        reports.forEach(
                l -> reportMap.putIfAbsent(l.getReportNo(), l)
        );
        // 创建一个 Set 用于存储不存在的 ReportNo
        Set<String> nonExistentReportNos = new HashSet<>();
        // 验证每个 ReportNo 是否存在于数据库中
        reportMap.values().forEach(l -> {
            TrfReportPO trfReportPO = trfReportDomainService.selectByReportNo(l.getReportNo());
            if (Func.isEmpty(trfReportPO)) {
                nonExistentReportNos.add(l.getReportNo());
            }
        });

        /**
         * 修改Report ReviewConclusion （当前SCI 没有保存Report信息，故而逻辑：从RD 查询报告信息，修改ReportViewConclusion 然后再次import）
         * 1、查询Report Data from RD
         * 2、修改Report.reviewConclusion
         * 3、importReportData to RD
         *
         * 问题：
         * 1、是否需要判断RD.Report的状态？ - - - Customer 发起review Conclusion时，Report已经rework等；
         *  - - - 暂时不处理
         */
        List<TrfReportDTO> reportList = new ArrayList<>(reportMap.values()).stream().filter(r -> !nonExistentReportNos.contains(r.getReportNo())).collect(Collectors.toList());
        Optional<List<TrfReportDTO>> reportListOpt = CollectionUtils.isNotEmpty(reportList) ? Optional.of(reportList) : Optional.empty();
        // TODO 内部无trfList补充，暂不处理 - -- 这是更新什么？
        List<TrfReportDTO> trfReportDTOS = reportListOpt.map(rl -> trfDomainService.updateReportList(rl, req.getLabId())).orElse(Collections.emptyList());

        //虽然是ReportList 接口，，但实际是单个Report处理，，不作无畏的内存处理，直接遍历
        for (TrfReportDTO trfReportDTO : trfReportDTOS) {
            // 1、查询Report Data from RD
            BatchExportReportDataReq exportReportDataTrfNoReq = new BatchExportReportDataReq();
            exportReportDataTrfNoReq.setReportNos(Func.toStrList(trfReportDTO.getReportNo()));
            exportReportDataTrfNoReq.setLabId(trfReportDTO.getLab().getLabId());
            exportReportDataTrfNoReq.setLabCode(trfReportDTO.getLab().getLabCode());
            exportReportDataTrfNoReq.setSystemId(trfReportDTO.getSystemId());
            // 从RD获取Report信息
            BaseResponse<List<ReportDataDTO>> listBaseResponse = reportDataClient.batchExportReportData(exportReportDataTrfNoReq);
            if (Func.isEmpty(listBaseResponse) || Func.isEmpty(listBaseResponse.getData())) {
                log.error("ReportDataCollect query report Error！");
                nonExistentReportNos.add(trfReportDTO.getReportNo());
                break;
            }

            // 2、修改Report.reviewConclusion
            ReportDataDTO reportDataDTO = listBaseResponse.getData().get(0);
            if (Func.isEmpty(reportDataDTO) || Func.isEmpty(reportDataDTO.getHeader()) || Func.isEmpty(reportDataDTO.getHeader().getConclusion())) {
                log.error("ReportDataCollect query report Error！");
                nonExistentReportNos.add(trfReportDTO.getReportNo());
                break;
            }

            RdConclusionDTO rdConclusionDTO = reportDataDTO.getHeader().getConclusion();
            rdConclusionDTO.setReviewConclusion(trfReportDTO.getConclusion().getReviewConclusion());
            rdConclusionDTO.setComments(trfReportDTO.getConclusion().getComments());

            ImportReportDataReq importReportDataReq = new ImportReportDataReq();
            //根据Export Data 重新组装Import Req
            importReportDataReq = buildImportReq(reportDataDTO);

            importReportDataReq.setLabId(exportReportDataTrfNoReq.getLabId());
            importReportDataReq.setLabCode(exportReportDataTrfNoReq.getLabCode());
            importReportDataReq.setSystemId(trfReportDTO.getSystemId());
            importReportDataReq.skipCheck();
            importReportDataReq.setToken(null);
            importReportDataReq.setRequestId(IdUtil.fastSimpleUUID());
//        baseModel.setSourceProductLineCode();
//            importReportDataReq.setProductLineCode(labInfo.getProductLineAbbr());

            BaseResponse<Void> voidBaseResponse = reportDataClient.batchImportReportData(importReportDataReq);
            //判断保存成功
            Assert.isTrue(ResponseCode.SUCCESS.getCode() == voidBaseResponse.getStatus(), voidBaseResponse.getStatus(), voidBaseResponse.getMessage());

            // 如果根据refSystemId找到客户订阅。更新标记
            Optional.ofNullable(trfReportDTO.getReportNo())
                    .map(reportNo -> trfReportDomainService.selectByReportNo(reportNo))
                    .map(trfReportPO -> trfDomainService.selectByTrfId(trfReportPO.getTrfId()))
                    .map(TrfInfoPO::getRefSystemId)
                    .ifPresent(refSystemId ->
                            eventSubscribeService.findByRefSystemIdAndEventCode(refSystemId, TRF_ACTION_REVIEW_CONCLUSION).stream()
                                    .filter(subscriber -> subscriber.getSubscriber().equals(refSystemId))
                                    .findAny()
                                    .ifPresent(subscribeDTO -> {
                                        trfReportDomainService.updateTrfReportDeliveryFlag(ImmutableList.of(trfReportDTO.getReportNo()), refSystemId);
                                        if(RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.TARGET_INSPECTORIO)) {
                                            trfReportDomainService.updateTrfReportDeliveryFlag(ImmutableList.of(trfReportDTO.getReportNo()), RefSystemIdEnum.Target.getRefSystemId());
                                        }
                                    })
                    );

            // by customer维度寻找邮件发送配置，根据条件判断是否需要发送邮件
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put(Constants.LAB_CODE_OF_BASIC_FIELD_MAPPING, exportReportDataTrfNoReq.getLabCode());
            requestMap.put(Constants.REPORT_NO, trfReportDTO.getReportNo());
            requestMap.put(Constants.REVIEW_CONCLUSION, rdConclusionDTO.getReviewConclusion());
            requestMap.put(Constants.REF_SYSTEM_ID, req.getSystemId());
            requestMap.put(Constants.ATTACHMENT_VALUE, JSONObject.toJSONString(trfReportDTO));
            requestMap.put(Constants.SUPPLIER_NAME, "");
            requestMap.put(Constants.CONCLUSION_REMARK, rdConclusionDTO.getConclusionRemark());
            requestMap.put(Constants.COMMENTS, rdConclusionDTO.getComments());
            LabInfoDTO labCodeInfoByLabCodeFromCache = frameWorkClient.getLabCodeInfoByLabCodeFromCache(exportReportDataTrfNoReq.getLabCode(), Func.toStr(req.getSystemId()));
            if (Func.isEmpty(labCodeInfoByLabCodeFromCache)) {
                labCodeInfoByLabCodeFromCache = new LabInfoDTO();
            }
            requestMap.put(Constants.COUNTRY_NAME,labCodeInfoByLabCodeFromCache.getCountryName() );
            requestMap.put(Constants.LOCATION_NAME, labCodeInfoByLabCodeFromCache.getLocationName());
            RdOrderDTO order = reportDataDTO.getOrder();
            if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getCustomerList())) {
                List<RdCustomerDTO> customerList = order.getCustomerList();
                RdCustomerDTO rdCustomerDTO = customerList.stream().filter(l -> Objects.equals(l.getCustomerUsage(), CustomerUsage.Supplier.getUsage())).findFirst().orElse(null);
                if (Func.isNotEmpty(rdCustomerDTO)) {
                    String customerName = rdCustomerDTO.getCustomerName();
                    requestMap.put(Constants.SUPPLIER_NAME, customerName);
                }
            }
            sendReviewConclusionEmail(requestMap, reportList.get(0).getTrfList().get(0).getRefSystemId());
        }

        // 再次根据rd的查询结果过滤
        trfReportDTOS = trfReportDTOS.stream().filter(report -> !nonExistentReportNos.contains(report.getReportNo())).collect(Collectors.toList());

        if (Func.isNotEmpty(trfReportDTOS)) {
            /**
             * 这个事件不能在for循环外面，因为ReviewConclusion 需要根据report维度分发
             */
            TrfActionEvent event = EventUtils.newTrfActionEvent(TrfActionEnum.SYNC_REVIEW_CONCLUSION.getCode());
            SciRequestContext requestContext = ProductLineContextHolder.retrieveSciRequestContext();
            TrfFullDTO trfFullDTO = new TrfFullDTO();

            TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
            trfHeaderDTO.setSystemId(req.getSystemId());
//            TrfLabDTO lab = new TrfLabDTO();
//            lab.setLabCode(trfReportDTOS.get(0).getLab().getLabCode());
//            trfHeaderDTO.setLab(lab);
            trfHeaderDTO.setLab(trfReportDTOS.get(0).getLab());
            trfFullDTO.setHeader(trfHeaderDTO);

            trfFullDTO.setReportList(reportList);
            event.setPayload(trfFullDTO);
            event.setTriggerBy(TrfEventTriggerBy.PREORDER);
            event.setRefSystemId(reportList.get(0).getTrfList().get(0).getRefSystemId());
            event.setTrfNo(reportList.get(0).getTrfList().get(0).getTrfNo());
//            event.setRefSystemId(req.getSystemId());
            event.setSystemId(req.getSystemId());
            event.setProductLineCode(req.getBuCode());
            event.setSourceId(requestContext.getRequestId());
            event.setExtId(reportList.get(0).getReportNo());
            applicationEventPublisher.publishEvent(event);
        }

        if (!nonExistentReportNos.isEmpty() && Objects.nonNull(req.getSystemId())) {
            trySendEmail(req, nonExistentReportNos, reports);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "reportNo: " + Joiner.on(",").join(nonExistentReportNos) + " not exists");
        }

        return CustomResult.newSuccessInstance();
    }

    private void sendReviewConclusionEmail(Map<String, Object> requestMap, Integer refSystemId) {
        if (Func.isEmpty(requestMap)) {
            return;
        }
        String labCode = requestMap.get(Constants.LAB_CODE_OF_BASIC_FIELD_MAPPING).toString();
        String reportNo = requestMap.get(Constants.REPORT_NO).toString();
        String value = requestMap.get(Constants.REVIEW_CONCLUSION).toString();
        String attachmentValue = requestMap.get(Constants.ATTACHMENT_VALUE).toString();

        Optional<CustomerGeneralConfig> customerGeneralConfigOpt = Optional.ofNullable(configClient.getConfig(refSystemId, Constants.CONFIG_CUSTOMER_CONFIG))
                .map(configValue -> JSON.parseObject(configValue, CustomerGeneralConfig.class));

        if (!customerGeneralConfigOpt.isPresent()) {
            return;
        }

        CustomerGeneralConfig customerGeneralConfig = customerGeneralConfigOpt.get();
        CustomerGeneralConfig.Emails emails = customerGeneralConfig.getEmails();
        if (Func.isEmpty(emails) || Func.isEmpty(emails.getReviewConclusion())) {
            return;
        }
        CustomerGeneralConfig.EmailNode reviewConclusion = emails.getReviewConclusion();

        String mailSubject = reviewConclusion.getMailSubject();
        String mailText = reviewConclusion.getMailText();
        Map<String, List<String>> mailTo = reviewConclusion.getMailTo();
        Set<String> labMailCc = reviewConclusion.getMailCc();
        String conditionPattern = reviewConclusion.getScript();
        if (Func.isNotBlank(conditionPattern)) {
            Map<String, Object> map = new HashMap<>();
            map.put(Constants.REVIEW_CONCLUSION, value);
            Bindings bindings = new AviatorBindings();
            bindings.putAll(map);
            boolean b = ConditionUtils.conditionEval(conditionPattern, bindings);
            if (!b) {
                return;
            }
        }
        StrSubstitutor formatter = new StrSubstitutor(requestMap);
        if (Func.isBlank(mailSubject)) {
            return;
        }
        Set<String> mailToSend = new HashSet<>();
        List<String> strings = mailTo.get(labCode);
        if (Func.isNotEmpty(strings)) {
            // 转set
            mailToSend.addAll(strings);
        } else {
            mailToSend.addAll(reviewConclusion.getDefaultMailTo());
        }
        EMailRequest eMailRequestBuilder = EMailRequest.builder().mailSubject(formatter.replace(mailSubject))
                .mailText(formatter.replace(mailText))
                .mailTo(mailToSend)
                .mailCc(labMailCc).build();

        if (Func.isNotBlank(attachmentValue) && reviewConclusion.isHasAttachment()) {
            String codedFileName = "attachmentFile" + refSystemId + reportNo + ".json";
            // 将attachmentValue转成文件
            try {
                File file = new File(codedFileName);
                FileOutputStream fileOutputStream = new FileOutputStream(file);
                fileOutputStream.write(encodeToJsonBase64(attachmentValue));
                fileOutputStream.close();
                String fileStream = compressAndEncodeFile(codedFileName);
                boolean isDeleted = new File(codedFileName).delete();
                eMailRequestBuilder.setByteStreamAttachments(Arrays.asList(EMailRequest.ByteStreamAttachments.builder().fileName(codedFileName).fileGzipBytesBase64(fileStream).build()));
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        TempSendEmailExecutor.asyncExecute(() -> emailClient.sendEmail(eMailRequestBuilder));
    }

    public static byte[] encodeToJsonBase64(String json) {
        // 将JSON字符串转换为字节数组
        byte[] jsonBytes = json.getBytes(java.nio.charset.StandardCharsets.UTF_8);

        // 使用Base64编码器将字节数组转换为Base64编码字符串
        String base64EncodedJson = Base64.getEncoder().encodeToString(jsonBytes);

        return jsonBytes;
    }

    public String compressAndEncodeFile(String filePath) throws IOException {
        try (FileInputStream fileInputStream = new FileInputStream(filePath);
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = fileInputStream.read(buffer)) != -1) {
                gzipOutputStream.write(buffer, 0, len);
            }
            gzipOutputStream.finish();
            byte[] compressedBytes = byteArrayOutputStream.toByteArray();
            return Base64.getEncoder().encodeToString(compressedBytes);
        }
    }

    private void trySendEmail(SyncReportToTrfReq req, Set<String> nonExistentReportNosArgs, List<TrfReportDTO> reports) {
        Set<String> nonExistentReportNos = nonExistentReportNosArgs;
        Optional<CustomerGeneralConfig> customerGeneralConfigOpt = Optional.ofNullable(configClient.getConfig(req.getSystemId(), Constants.CONFIG_CUSTOMER_CONFIG))
                .map(configValue -> JSON.parseObject(configValue, CustomerGeneralConfig.class));

        if (!customerGeneralConfigOpt.isPresent()) {
            return;
        }

        CustomerGeneralConfig customerGeneralConfig = customerGeneralConfigOpt.get();
        RecipientConfig recipientConfig = customerGeneralConfig.getReviewConclusionRecipient();
        if (Objects.isNull(recipientConfig) || CollectionUtils.isEmpty(recipientConfig.getMailTo())) {
            return;
        }

        if(req.getSystemId().equals(RefSystemIdEnum.BigLots.getRefSystemId())) {
            try {
                List<CPReportsData> cpReports = inspectionClient.getCPReports(nonExistentReportNos);
                Map<String, Boolean> inspectionResult = cpReports.stream().collect(Collectors.toMap(CPReportsData::getReportNo, CPReportsData::isInspectionReport, (v1, v2) -> v1));
                nonExistentReportNos = nonExistentReportNos.stream().filter(no -> !inspectionResult.containsKey(no) || !inspectionResult.get(no)).collect(Collectors.toSet());
            }catch (Throwable t) {
                //ignore
            }

            if(CollectionUtils.isEmpty(nonExistentReportNos)) {
                return;
            }
        }

        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy hh:mm:ss"));
        EMailRequest.EMailRequestBuilder eMailRequestBuilder = EMailRequest.builder()
                .mailSubject("Missing fields during the creation of the " + customerGeneralConfig.getCustomerNameEn() + " Inbound file at " + time)
                .mailTo(new HashSet<>(recipientConfig.getMailTo()));
        Optional.ofNullable(recipientConfig.getMailCc())
                .flatMap(cc -> CollectionUtils.isNotEmpty(cc) ? Optional.of(cc) : Optional.empty())
                .ifPresent(cc -> eMailRequestBuilder.mailCc(new HashSet<>(cc)));
        eMailRequestBuilder.mailText("Dear User: <br/>" +
                "The following fields were missing from the attached inbound " + customerGeneralConfig.getCustomerNameEn() + " file.<br/>" +
                customerGeneralConfig.getCustomerNameEn() + " inbound failed, Can't find the report: " + Joiner.on(";").join(nonExistentReportNos));
        Optional<String> filePath = Optional.ofNullable(reports)
                .flatMap(reports0 -> CollectionUtils.isNotEmpty(reports0) ? reports0.stream().findFirst() : Optional.empty())
                .map(TrfReportDTO::getReportFileList)
                .flatMap(fileList -> CollectionUtils.isNotEmpty(fileList) ? fileList.stream().findFirst() : Optional.empty())
                .map(TrfFileDTO::getFilePath);
        Optional<String> fileName = Optional.ofNullable(reports)
                .flatMap(reports0 -> CollectionUtils.isNotEmpty(reports0) ? reports0.stream().findFirst() : Optional.empty())
                .map(TrfReportDTO::getReportFileList)
                .flatMap(fileList -> CollectionUtils.isNotEmpty(fileList) ? fileList.stream().findFirst() : Optional.empty())
                .map(TrfFileDTO::getFileName);
        filePath.ifPresent(cloudId -> {
            try {
                String downloadUrl = fileClient.downloadByCloudID(cloudId);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream);
                fileClient.downloadFile(downloadUrl, gzipOutputStream);
                gzipOutputStream.finish();
                byte[] compressedBytes = byteArrayOutputStream.toByteArray();
                String base64Content = Base64.getEncoder().encodeToString(compressedBytes);
                eMailRequestBuilder.byteStreamAttachments(
                        ImmutableList.of(
                                EMailRequest.ByteStreamAttachments.builder()
                                        .fileName(fileName.orElse(cloudId))
                                        .fileGzipBytesBase64(base64Content)
                                        .build()
                        )
                );
            } catch (Throwable t) {
                log.error("download report file error", t);
            }
        });
        TempSendEmailExecutor.asyncExecute(() -> emailClient.sendEmail(eMailRequestBuilder.build()));
    }

    private ImportReportDataReq buildImportReq(ReportDataDTO reportDataDTO) {

        ImportReportDataReq req = new ImportReportDataReq();
        Func.copy(reportDataDTO, req);

        List<RdOrderDTO> orderList = new ArrayList<>();
        orderList.add(reportDataDTO.getOrder());
        req.setOrderList(orderList);

        List<RdReportDTO> reportList = new ArrayList<>();
        reportList.add(reportDataDTO.getHeader());
        req.setReportList(reportList);

        return req;
    }

    public CustomerConfirmReportFlag confirmReport(CustomerConfirmedReport confirmedReport) {
        Assert.notNull(confirmedReport, "The confirmedReport request is required");
        Assert.notNull(confirmedReport.getRefSystemId(), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        Assert.notNull(confirmedReport.getTrfNo(), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        Assert.notNull(confirmedReport.getReportNo(), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The reportNo is required");
        return trfDomainService.customerConfirmReport(confirmedReport.getRefSystemId(), confirmedReport.getTrfNo(), confirmedReport.getReportNo());
    }

    public CustomerConfirmReportTrf<CustomerConfirmReportFlag> queryReportConfirmFlag(CustomerConfirmReportTrf<ReportNo> queryCustomerConfirmReport) {
        Assert.notNull(queryCustomerConfirmReport, "The query request is required");
        Assert.notNull(queryCustomerConfirmReport.getRefSystemId(), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The refSystemId is required");
        Assert.notNull(queryCustomerConfirmReport.getTrfNo(), ResponseCode.INTERNAL_SERVER_ERROR.getCode(), "The trfNo is required");
        if(CollectionUtils.isEmpty(queryCustomerConfirmReport.getReportList())) {
            return CustomerConfirmReportTrf.empty(queryCustomerConfirmReport.getRefSystemId(), queryCustomerConfirmReport.getTrfNo());
        }
        List<String> reportNoList = Optional.ofNullable(queryCustomerConfirmReport.getReportList())
                .map(rs -> rs.stream().map(ReportNo::getReportNo).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        return trfDomainService.queryReportConfirmFlag(queryCustomerConfirmReport.getRefSystemId(), queryCustomerConfirmReport.getTrfNo(), reportNoList);
    }

}
