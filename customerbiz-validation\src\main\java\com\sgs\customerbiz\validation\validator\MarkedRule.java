package com.sgs.customerbiz.validation.validator;

import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;

import java.util.Map;

public interface MarkedRule extends Rule{

    @Override
    default ValidationResult validate(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig) {
        throw new UnsupportedOperationException("MarkedRule not supported validate(value,params,fieldConfig,ruleConfig)");
    }
}
