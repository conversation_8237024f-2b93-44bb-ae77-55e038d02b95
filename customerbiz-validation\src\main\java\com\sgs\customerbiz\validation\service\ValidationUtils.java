package com.sgs.customerbiz.validation.service;

import com.alibaba.fastjson.JSONPath;
import com.sgs.customerbiz.validation.service.parser.ValueParser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class ValidationUtils {

    /**
     * 根据不同的表达式类型，从ValidationData对象中提取相应的值。
     * 支持的表达式类型包括：SAMPLEATTRLIST、JSONARRAY、JSONOBJECT以及直接通过JSONPath表达式获取值。
     * 对于SAMPLEATTRLIST类型，它从sampleList中提取特定labelCode的值列表。
     * 对于JSONARRAY和JSONOBJECT类型，它们分别解析对应的JSON数组和JSON对象。
     * 对于其他类型的表达式，直接通过JSONPath表达式获取值。
     *
     * @param data                  包含验证数据的ValidationData对象
     * @param fieldValidationConfig 验证配置，包含要提取的值的表达式信息
     * @return 根据表达式类型提取的值，可能是一个列表、一个对象或者一个基本数据类型
     */
    public static Object getValue(ValidationData data, FieldValidationConfig fieldValidationConfig, Map<String, ValueParser> valueParserMap) {
        String[] expr = fieldValidationConfig.getExpr().split(":");
        ValueParser defaultParser = valueParserMap.get(ValueParser.DEFAULT);
        if (expr.length == 1) {
            return defaultParser.parse(data, fieldValidationConfig.getExpr());
        }

        if (expr.length == 2) {
            ValueParser parser = valueParserMap.getOrDefault(expr[0], defaultParser);
            return parser.parse(data, fieldValidationConfig.getExpr());
        }

        throw new IllegalArgumentException("formatter of expr must be [Parser| ]:expr");

    }

    public static Map<String, Object> extractParamValue(Map<String, Object> paramTable, Object data, Map<String, ValueParser> valueParserMap) {
        Objects.requireNonNull(paramTable, "paramTable is null");
        Map<String, Object> paramValueMap = new HashMap<>();
        for (Map.Entry<String, Object> param : paramTable.entrySet()) {
            if (param.getValue() instanceof String) {
                String strValue = (String) param.getValue();
                if (strValue.startsWith("$")) {
                    paramValueMap.put(param.getKey(), JSONPath.eval(data, strValue));
                } else if (needParser(strValue)) {
                    String[] expr = strValue.split(":");
                    ValueParser defaultParser = valueParserMap.get(ValueParser.DEFAULT);
                    ValueParser parser = valueParserMap.getOrDefault(expr[0], defaultParser);
                    paramValueMap.put(param.getKey(), parser.parse(data, strValue));
                } else {
                    paramValueMap.put(param.getKey(), param.getValue());
                }
            } else {
                paramValueMap.put(param.getKey(), param.getValue());
            }
        }
        return paramValueMap;
    }

    private static boolean needParser(String exprStr) {
        String[] expr = Optional.ofNullable(exprStr).orElse("").split(":");
        return expr.length == 2;
    }

    public static boolean isEmptyCollection(Collection<?> collection) {
        if(CollectionUtils.isEmpty(collection)){
            return true;
        }
        return collection.stream().allMatch(item -> Objects.isNull(item) || (item instanceof String && StringUtils.isBlank((String) item)));
    }

}
