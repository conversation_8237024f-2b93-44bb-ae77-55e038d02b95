package com.sgs.customerbiz.app.component.action.customerTrf;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.app.process.ImportToTRFProcess;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.context.CustomerTRFContext;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.GetCustomerConfigReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfBindReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.customerbiz.model.trf.enums.TrfImportMode;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Component
@LiteflowComponent(id = "customerTrfQueryActionComponent", name = "customerTrf info查询")
public class CustomerTrfQueryActionComponent extends BaseActionComponent {

    private final SciTrfBizService sciTrfBizService;
    private final ImportToTRFProcess importToTRFProcess;
    private final ConfigClient configClient;
    private final TrfOrderDomainService trfOrderDomainService;
    private final TrfDomainService trfDomainService;

    public CustomerTrfQueryActionComponent(SciTrfBizService sciTrfBizService, ImportToTRFProcess importToTRFProcess, ConfigClient configClient, TrfOrderDomainService trfOrderDomainService, TrfDomainService trfDomainService) {
        this.sciTrfBizService = sciTrfBizService;
        this.importToTRFProcess = importToTRFProcess;
        this.configClient = configClient;
        this.trfOrderDomainService = trfOrderDomainService;
        this.trfDomainService = trfDomainService;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        CustomerTRFContext customerTRFContext = (CustomerTRFContext) requestContext;
        TrfImportResult trfImportResult = getCustomerTrfInfo(customerTRFContext);
        customerTRFContext.setResult(trfImportResult);
        return customerTRFContext;
    }

    @Override
    public void process() throws Exception {
        CustomerTRFContext customerTRFContext = this.getContextBean(CustomerTRFContext.class);
        TrfImportResult trfImportResult = getCustomerTrfInfo(customerTRFContext);
        customerTRFContext.setResult(trfImportResult);
    }

    private TrfImportResult getCustomerTrfInfo(CustomerTRFContext customerTRFContext) {
        TrfBindReq bindTrfReq = customerTRFContext.getTrfBindReq();
        TrfInfoPO trfInfoPO = sciTrfBizService.getTrfInfoPO(bindTrfReq);
        GetCustomerConfigReq customerConfigReq = new GetCustomerConfigReq();
        customerConfigReq.setRefSystemId(bindTrfReq.getRefSystemId());
        ConfigInfo customerConfig = configClient.getCustomerConfig(customerConfigReq);
        Set<String> supportImportModes = Optional.ofNullable(customerConfig)
                .map(ConfigInfo::getConfigValue)
                .filter(StringUtils::isNotBlank)
                .map(configVal -> JSON.parseObject(configVal, CustomerGeneralConfig.class))
                .map(CustomerGeneralConfig::getSupportImportModes)
                .filter(CollectionUtils::isNotEmpty)
                .orElse(CustomerGeneralConfig.DEFAULT_SUPPORT_IMPORT_MODES);

        if(Objects.isNull(trfInfoPO) && !supportImportModes.contains(TrfImportMode.TRF_IMPORT_MODE_PULL)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.SCITRFBIZSERVICE,
                    ErrorFunctionTypeEnum.BIND,
                    ErrorTypeEnum.DATANOTFOUND);
            throw new CustomerBizException(errorCode, "can't bind trf! the refSystem not support pull mode and trf not found by request " + bindTrfReq);
        }

        if(Objects.nonNull(trfInfoPO) && Objects.equals(trfInfoPO.getSource(), TrfSourceType.Order2TRF.getSourceType())) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorBizModelEnum.SCITRFBIZSERVICE,
                    ErrorFunctionTypeEnum.BIND,
                    ErrorTypeEnum.REVISE_STATUS_ERROR);
            throw new CustomerBizException(errorCode, "Trf source is OrderToTrf. Other Orders cannot be bound to this Trf. ");
        }

        TrfImportResult trfImportResult;
        if (ObjectUtil.isNotNull(trfInfoPO) && !trfInfoPO.getStatus().equals(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus())) {
            trfImportResult = sciTrfBizService.getTrfImportResult(bindTrfReq, trfInfoPO);
        } else {
            if(supportImportModes.contains(TrfImportMode.TRF_IMPORT_MODE_PULL)) {
                TrfImportReq trfImportReq = sciTrfBizService.buildImportTrfReq(bindTrfReq);
                trfImportResult = importToTRFProcess.doProcess(trfImportReq);
            } else {
                trfImportResult = sciTrfBizService.getSgsTrfByTrfNo(bindTrfReq.getTrfNo());
            }

        }
        return trfImportResult;
    }
}
