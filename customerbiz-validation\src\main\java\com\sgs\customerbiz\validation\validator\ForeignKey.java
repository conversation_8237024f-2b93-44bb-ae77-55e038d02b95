package com.sgs.customerbiz.validation.validator;

import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ForeignKey校验器
 * 校验参数的value（有可能是个单值，大概率是一个set的ID），是否存在于params这个Map里面一个key=referenceIds的set值里面。
 * 如果不存在，就要报错，如果全部都存在就通过校验
 */
@Component
public class ForeignKey extends AbstractOkIfNullValueRule {

    public static final String REFERENCE_IDS = "referenceIds";

    @Override
    protected ValidationResult doValidateIfValueNotNull(Object value,
                                                        Map<String, Object> params,
                                                        FieldValidationConfig fieldValidationConfig,
                                                        FieldRuleConfig ruleConfig) {
        // 获取参考ID集合
        Object referenceIdsObj = params.get(REFERENCE_IDS);
        if (referenceIdsObj == null) {
            throw new IllegalArgumentException("Parameter 'referenceIds' is required but not found in params");
        }

        Set<?> referenceIds;
        if (referenceIdsObj instanceof Set) {
            referenceIds = ((Set<?>) referenceIdsObj).stream().filter(Objects::nonNull).collect(Collectors.toSet());
        } else if (referenceIdsObj instanceof Collection) {
            referenceIds = ((Collection<?>) referenceIdsObj).stream().filter(Objects::nonNull).collect(Collectors.toSet());
        } else {
            throw new IllegalArgumentException("Parameter 'referenceIds' must be a Set or Collection, but found: " + referenceIdsObj.getClass());
        }

        // 如果参考ID集合为空，则任何值都不会通过校验
        if (referenceIds.isEmpty()) {
            return ValidationResult.fail(fieldValidationConfig, ruleConfig, value, params);
        }

        // 校验单个值或集合中的每个值
        if (value instanceof Collection) {
            Collection<?> valueCollection = (Collection<?>) value;
            Set<?> valueSet = valueCollection.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            if (!referenceIds.containsAll(valueSet)) {
                return ValidationResult.fail(fieldValidationConfig, ruleConfig, value, params);
            }
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        } else {
            // 单个值校验
            if (referenceIds.contains(value)) {
                return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
            } else {
                return ValidationResult.fail(fieldValidationConfig, ruleConfig, value, params);
            }
        }
    }

    @Override
    public Set<String> parameterNames() {
        return ImmutableSet.of(REFERENCE_IDS);
    }
}
